<?php

namespace App\Services;

use App\Contribution;
use App\Distributor;
use App\Sale;
use App\Product;
use App\Mapping;
use App\SaleDetail;
use App\Models\SaleFactor;
use App\Models\PharmacyType;
use App\Services\Enums\Ceiling;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class NonUnifiedSalesService
{
    private $fileId;
    private $type;
    private bool $mappingWithDistributor;
    private $salesMappingSettings;
    private $salesContributionBaseOn;
    private int $cacheSavedHours = 2;
    private bool $postMappingSettings;

    public function __construct(
        int  $fileId,
             $type,
        bool $mappingWithDistributor,
             $salesMappingSettings,
             $salesContributionBaseOn,
        bool $postMappingSettings
    )
    {
        $this->fileId = $fileId;
        $this->type = $type;
        $this->mappingWithDistributor = $mappingWithDistributor;
        $this->salesMappingSettings = $salesMappingSettings;
        $this->salesContributionBaseOn = $salesContributionBaseOn;
        $this->postMappingSettings = $postMappingSettings;
    }

    public function manageSales(array $row, Sale $sale, Product $product): void
    {
        $this->addPostMapping($row, $sale->distributor);
        $mappings = $this->getMappings($row, $sale->distributor);
        $distributorLines = $sale->distributor->lines()->get()->pluck('id');
        $lineIds = $this->getProductLineIds($product,$sale->distributor,$distributorLines, $sale->date);

        $sale->mappings()->sync($mappings->pluck("id")->toArray());

        $sales = $this->generateSalesData($mappings, $sale, $product, $lineIds);

        if (!empty($sales)) {
            SaleDetail::insert($sales);
        }
    }

    private function addPostMapping(array $row, $distributor): void
    {
        if (!$this->postMappingSettings) return;

        $cacheKey = "file_id:{$this->fileId}_brick_code:{$row['brick']}_mapping_type_id:{$this->type}";

        $exists = Cache::remember(
            $cacheKey,
            now()->addHours($this->cacheSavedHours),
            fn() => Mapping::where('code', $row['brick'])
                ->where('mapping_type_id', $this->type)
                ->exists()
        );

        if ($exists) return;

        $pharmacyType = $row['type']
            ? PharmacyType::firstOrCreate(['name' => $row['type']])
            : new PharmacyType();

        DB::statement('CALL create_normal_mapping(?)', [
            json_encode([
                'code' => $row['brick'],
                'mapping_type_id' => $this->type,
                'line_id' => null,
                'pharmacy_type_id' => $pharmacyType->id,
                'distributor_id' => $this->mappingWithDistributor ? $distributor->id : null,
                'name' => $row['brick_name'],
                'address' => $row['address'],
                'from_date' => Carbon::now()->firstOfYear()->toDateTimeString(),
                'to_date' => null,
                'file_id' => $this->fileId,
                'div_id' => $this->salesMappingSettings === 'Division' ? $row[' '] : null,
                'brick_id' => $this->salesMappingSettings === 'Brick' ? $row['brick_id'] : null,
                'percent' => 100,
            ])
        ]);

        Cache::put($cacheKey, true,now()->addHours($this->cacheSavedHours));
    }

    private function getMappings(array $row, $distributor): Collection
    {
        $cacheKey = "file_id:{$this->fileId}_brick_code:{$row['brick']}_mapping_type_id:{$this->type}_distributor_id:{$distributor->id}";

        return Cache::remember(
            $cacheKey,
            now()->addHours($this->cacheSavedHours),
            function () use ($row, $distributor) {
                $query = Mapping::select(["id", "line_id", "code"])
                    ->with(["saleFactors", "details:mapping_id,percent,div_id,brick_id"])
                    ->where('code', $row['brick'])
                    ->where('mapping_type_id', $this->type)
                    ->where('mappings.from_date', '<=', now())
                    ->where(function ($q) {
                        $q->where('mappings.to_date', '>', now())
                            ->orWhereNull('mappings.to_date');
                    });

                if ($this->mappingWithDistributor) {
                    $query->where('distributor_id', $distributor->id);
                }

                return $query->get();
            }
        );
    }

    private function getProductLineIds(Product $product,Distributor $distributor, Collection $distributorLines, $date): Collection
    {
        $cacheKey = "file_id:{$this->fileId}_line_ids_on_product_id:{$product->id}_on_distributor_id:{$distributor->id}";

        return Cache::remember(
            $cacheKey,
            now()->addHours($this->cacheSavedHours),
            function () use ($product, $distributorLines, $date) {
                return $product->allLines()
                    ->where('line_products.from_date', '<=', $date)
                    ->where(function ($q) use ($date) {
                        $q->where('line_products.to_date', '>', $date)
                            ->orWhereNull('line_products.to_date');
                    })
                    ->whereIntegerInRaw('lines.id', $distributorLines)
                    ->pluck("id");
            }
        );

    }

    private function generateSalesData($mappings, Sale $sale, Product $product, Collection $lineIds): array
    {
        $sales = [];
        foreach ($mappings as $mapping) {
            $id = $mapping->saleFactors->first()->id;
            $sales = array_merge($sales, match ($id) {
                SaleFactor::NORMAL => $this->addNormalSales($sale, $product, $mapping, $lineIds),
                SaleFactor::CR_TARGET => $this->addContributionSales($sale, $product, $mapping),
                SaleFactor::CR_SALES, SaleFactor::DIRECT_SALES => $this->addSales($sale, $product, $mapping, $lineIds)
            });
        }
        return $sales;
    }

    private function addNormalSales(Sale $sale, Product $product, Mapping $mapping, Collection $lineIds): array
    {
        $ids = $this->IntersectLine($lineIds, $mapping);

        $salesData = [];

        foreach ($ids as $lineId) {
            foreach ($mapping->details as $item) {
                if ($this->salesMappingSettings == 'Division') {
                    $salesData[] = $this->createSaleDetailArray($sale, $lineId, $item->div_id, null, $item->percent);
                } else {
                    $divisions = $this->getDivisionsForProduct($product, $lineId, $item->brick_id, $sale->date);
                    foreach ($divisions as $division) {
                        $sharablePercentage = ($division->ratio / 100) * ($item->percent / 100);
                        $salesData[] = $this->createSaleDetailArray($sale, $lineId, $division->line_division_id, $item->brick_id, $sharablePercentage);
                    }
                }
            }
        }

        return $salesData;
    }

    private function addContributionSales(Sale $sale, Product $product, Mapping $mapping): array
    {
        $contributions = Contribution::select([
            "div_id",
            "brick_id",
            "contribution"
        ])->where("line_id", $mapping->line->id)
            ->where("product_id", $product->id)
            ->whereMonth("date", now()->format("m"))
            ->get();


        return $contributions->map(function ($item) use ($sale, $mapping) {
            return $this->createSaleDetailArray($sale, $mapping->line->id, $item->div_id, $item->brick_id, $item->contribution / 100);
        })->toArray();
    }

    private function addSales(Sale $sale, Product $product, Mapping $mapping, Collection $lineIds): array
    {
        $lineId = $this->IntersectLine($lineIds, $mapping);
        if ($lineId->isEmpty()) return [];
        $divisionsBricks = SalesDistributionService::getDivisionsAndBrickForDistribution(
            $sale->date,
            $lineIds->toArray(),
            $product->id,
            $this->salesContributionBaseOn
        );

        $salesData = $divisionsBricks->map(function ($divisionBrick) use ($sale) {
            return $this->createSaleDetailArray($sale, $sale->id, $divisionBrick->div_id, $divisionBrick->brick_id, $divisionBrick->percentage);
        })->toArray();

        $sale->update([
            "ceiling" => Ceiling::DISTRIBUTED
        ]);

        return $salesData;
    }

    private function IntersectLine(Collection $lineIds, Mapping $mapping): Collection
    {
        $cacheKey = "file_id:{$this->fileId}_intersection:" . Hash::make($lineIds->toJson() . '_' . $mapping->id);

        return Cache::remember(
            $cacheKey,
            now()->addHours($this->cacheSavedHours),
            fn() => $mapping->line_id ? $lineIds->intersect([$mapping->line_id]) : $lineIds
        );
    }

    private function getDivisionsForProduct(Product $product, $lineId, $brickId, $date): Collection
    {
        $cacheKey = "file_id:{$this->fileId}_product_id:{$product->id}_line_id:{$lineId}_brick_id:{$brickId}_date:{$date}";

        return Cache::remember(
            $cacheKey,
            now()->addHours($this->cacheSavedHours),
            function () use ($product, $lineId, $brickId, $date) {
                return $product->lineBricksInDates(Carbon::parse($date), Carbon::parse($date))
                    ->where("line_bricks.line_id", $lineId)
                    ->where("line_bricks.brick_id", $brickId)
                    ->where('line_bricks.from_date', '<=', $date)
                    ->where(function ($q) use ($date) {
                        $q->where('line_bricks.to_date', '>', $date)
                            ->orWhereNull('line_bricks.to_date');
                    })
                    ->get();
            }
        );
    }

    private function createSaleDetailArray(Sale $sale, $lineId, $divId, $brickId, $percentage): array
    {
        return [
            'sale_id' => $sale->id,
            'line_id' => $lineId,
            'div_id' => $divId,
            'brick_id' => $brickId,
            'date' => $sale->date,
            'quantity' => $percentage * $sale->quantity,
            'bonus' => $percentage * $sale->bonus,
            'value' => $percentage * $sale->value,
            'file_id' => $this->fileId,
            'created_at' => now(),
            'updated_at' => now(),
            'ratio' => $percentage
        ];
    }
}
