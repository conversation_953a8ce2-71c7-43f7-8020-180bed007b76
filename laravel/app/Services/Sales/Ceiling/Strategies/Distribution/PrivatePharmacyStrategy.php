<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SimpleDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\Traits\EnsureLimitedSaleMapping;
use App\Services\Sales\Ceiling\Strategies\Distribution\Traits\StrategicExcessHandler;
use App\Services\SalesDistributionService;
use Illuminate\Support\Facades\Log;

/**
 * Refactored Private Pharmacy Distribution Strategy
 *
 * This strategy uses simple 100% distribution for excess sales.
 * It follows SOLID principles and uses dependency injection.
 */
class PrivatePharmacyStrategy extends AbstractDistributionStrategy
{
    use StrategicExcessHandler;
    use EnsureLimitedSaleMapping;
    private ExcessDistributorInterface $excessDistributor;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SettingsProviderInterface   $settingsProvider,
        LimitCalculatorInterface    $limitCalculator,
        SaleDetailFactory           $saleDetailFactory,
        SaleCreator                 $saleCreator,
        SalesDistributionService    $salesService,
        SimpleDistributionAlgorithm $excessDistributor
    )
    {
        parent::__construct(
            $transactionManager,
            $settingsProvider,
            $limitCalculator,
            $saleDetailFactory,
            $saleCreator,
            $salesService
        );

        $this->excessDistributor = $excessDistributor;
    }


    /**
     * Get the DistributionType for this strategy
     *
     * @return DistributionType
     */
    protected function getDistributionType(): DistributionType
    {
        return DistributionType::PRIVATE_PHARMACY;
    }
}
