<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Sale;
use App\SaleDetail;
use Illuminate\Support\Facades\Log;

class SaleDetailFactory
{
    /**
     * Create sale details for a limited sale
     *
     * @param Sale $originalSale
     * @param Sale $limitedSale
     * @param mixed $ceilingSale
     * @return bool
     */
    public function createLimitedSaleDetails(Sale $originalSale, Sale $limitedSale, $ceilingSale): bool
    {
        $details = [];

        foreach ($originalSale->details as $saleDetail) {
            $percentage = $this->calculateDetailPercentage($saleDetail, $originalSale);

            $details[] = [
                'line_id' => $saleDetail->line_id,
                'div_id' => $saleDetail->div_id,
                'brick_id' => $saleDetail->brick_id,
                'quantity' => $limitedSale->quantity * $percentage,
                'bonus' => $limitedSale->bonus * $percentage,
                'value' => $limitedSale->value * $percentage,
                'date' => $ceilingSale->date,
                'sale_id' => $limitedSale->id,
                'file_id' => $saleDetail->file_id,
                'ratio' => round($percentage, 9),
                'created_at' => now(),
                'updated_at' => now()
            ];
        }

        return SaleDetail::insert($details);
    }

    /**
     * Create sale details from distribution ratios
     *
     * @param Sale $sale
     * @param array $distributionRatios
     * @param float $quantityMultiplier
     * @param float $bonusMultiplier
     * @param float $valueMultiplier
     * @param float $ratioMultiplier
     * @return array
     */
    public function createDetailsFromRatios(
        Sale  $sale,
        array $distributionRatios,
        float $quantityMultiplier = 1.0,
        float $bonusMultiplier = 1.0,
        float $valueMultiplier = 1.0,
        float $ratioMultiplier = 1.0
    ): array
    {
        Log::debug('SaleDetailFactory: Creating details from ratios', [
            'sale_id' => $sale->id,
            'sale_quantity' => $sale->quantity,
            'sale_value' => $sale->value,
            'sale_bonus' => $sale->bonus,
            'ratios_count' => count($distributionRatios),
            'quantity_multiplier' => $quantityMultiplier,
            'bonus_multiplier' => $bonusMultiplier,
            'value_multiplier' => $valueMultiplier,
            'ratio_multiplier' => $ratioMultiplier
        ]);

        $details = [];
        $totalCalculatedQuantity = 0;
        $totalCalculatedValue = 0;

        foreach ($distributionRatios as $ratio) {
            // Handle both object and array formats for ratio data
            $lineId = is_object($ratio) ? $ratio->line_id : $ratio['line_id'];
            $divId = is_object($ratio) ? $ratio->div_id : $ratio['div_id'];
            $brickId = is_object($ratio) ? $ratio->brick_id : $ratio['brick_id'];
            $percentage = is_object($ratio) ? $ratio->percentage : $ratio['percentage'];

            $calculatedQuantity = $percentage * $sale->quantity * $quantityMultiplier;
            $calculatedValue = $percentage * $sale->value * $valueMultiplier;

            $totalCalculatedQuantity += $calculatedQuantity;
            $totalCalculatedValue += $calculatedValue;

            $details[] = [
                'sale_id' => $sale->id,
                'line_id' => $lineId,
                'div_id' => $divId,
                'brick_id' => $brickId,
                'date' => $sale->date,
                'quantity' => $calculatedQuantity,
                'bonus' => $percentage * $sale->bonus * $bonusMultiplier,
                'value' => $calculatedValue,
                'file_id' => $sale->file_id,
                'ratio' => round($percentage * $ratioMultiplier , 9)
            ];
        }

        Log::debug('SaleDetailFactory: Details created from ratios', [
            'sale_id' => $sale->id,
            'details_count' => count($details),
            'total_calculated_quantity' => $totalCalculatedQuantity,
            'total_calculated_value' => $totalCalculatedValue,
            'original_sale_quantity' => $sale->quantity,
            'original_sale_value' => $sale->value,
            'quantity_match' => abs($totalCalculatedQuantity - ($sale->quantity * $quantityMultiplier)) < 0.01,
            'value_match' => abs($totalCalculatedValue - ($sale->value * $valueMultiplier)) < 0.01
        ]);

        return $details;
    }

    /**
     * Calculate detail percentage for a sale detail
     *
     * @param mixed $saleDetail
     * @param Sale $originalSale
     * @return float
     */
    private function calculateDetailPercentage($saleDetail, Sale $originalSale): float
    {
        if ($originalSale->quantity == 0) {
            return 0;
        }

        return $saleDetail->quantity / $originalSale->quantity;
    }

    /**
     * Bulk insert sale details with enhanced error handling and validation
     *
     * @param array $details
     * @return bool
     */
    public function insertDetails(array $details): bool
    {
        if (empty($details)) {
            Log::error('SaleDetailFactory: No details to insert - distribution failure detected', [
                'details_count' => 0,
                'expected_behavior' => 'Distribution should create sale details',
                'impact' => 'This will result in orphaned sales without details',
                'recommended_action' => 'Check distribution ratio calculation and ensure valid ratios exist'
            ]);
            return false;
        }

        $totalQuantity = array_sum(array_column($details, 'quantity'));
        $totalValue = array_sum(array_column($details, 'value'));
        $uniqueSaleIds = array_unique(array_column($details, 'sale_id'));

        // Validate details before insertion
        $validationErrors = $this->validateDetailsBeforeInsertion($details);
        if (!empty($validationErrors)) {
            Log::error('SaleDetailFactory: Details validation failed before insertion', [
                'details_count' => count($details),
                'validation_errors' => $validationErrors,
                'unique_sale_ids' => $uniqueSaleIds,

            ]);
            return false;
        }

        Log::info('SaleDetailFactory: Inserting sale details', [
            'details_count' => count($details),
            'unique_sale_ids' => $uniqueSaleIds,
            'total_quantity' => $totalQuantity,
            'total_value' => $totalValue
        ]);

        try {
            $result = SaleDetail::insert($details);

            if ($result) {
                Log::debug('SaleDetailFactory: Sale details inserted successfully', [
                    'details_count' => count($details),
                    'unique_sale_ids' => $uniqueSaleIds
                ]);
            } else {
                Log::error('SaleDetailFactory: Sale details insertion failed', [
                    'details_count' => count($details),
                    'unique_sale_ids' => $uniqueSaleIds,
                    'reason' => 'SaleDetail::insert returned false'
                ]);
            }

            return $result;
        } catch (\Throwable $e) {
            Log::error('SaleDetailFactory: Exception during sale details insertion', [
                'details_count' => count($details),
                'unique_sale_ids' => $uniqueSaleIds,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);
            return false;
        }
    }

    /**
     * Validate details array before database insertion
     *
     * @param array $details
     * @return array Array of validation errors (empty if valid)
     */
    private function validateDetailsBeforeInsertion(array $details): array
    {
        $errors = [];

        foreach ($details as $index => $detail) {
            // Check required fields
            $requiredFields = ['sale_id', 'line_id', 'div_id', 'brick_id', 'quantity', 'date'];
            foreach ($requiredFields as $field) {
                if (!isset($detail[$field]) || $detail[$field] === null) {
                    $errors[] = "Detail at index {$index} missing required field: {$field}";
                }
            }

            // Check for ratio values that exceed database column limits
            if (isset($detail['ratio']) && $detail['ratio'] > 99999.9999999999) {
                $errors[] = "Detail at index {$index} has ratio value too large for database column: {$detail['ratio']}";
            }
        }

        return $errors;
    }
}
