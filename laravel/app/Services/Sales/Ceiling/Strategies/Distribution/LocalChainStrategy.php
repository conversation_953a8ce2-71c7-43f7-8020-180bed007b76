<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\HierarchicalChainDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\Traits\EnsureLimitedSaleMapping;
use App\Services\Sales\Ceiling\Strategies\Distribution\Traits\StrategicExcessHandler;
use App\Services\SalesDistributionService;

/**
 * Refactored Local Chain Distribution Strategy
 *
 * This strategy can be easily extended with custom distribution logic.
 * Currently uses simple distribution as a starting point.
 * It follows SOLID principles and uses dependency injection.
 */
class LocalChainStrategy extends AbstractDistributionStrategy
{

    use StrategicExcessHandler;
    use EnsureLimitedSaleMapping;
    private ExcessDistributorInterface $excessDistributor;

    public function __construct(
        TransactionManagerInterface            $transactionManager,
        SettingsProviderInterface              $settingsProvider,
        LimitCalculatorInterface               $limitCalculator,
        SaleDetailFactory                      $saleDetailFactory,
        SaleCreator                            $saleCreator,
        SalesDistributionService               $salesService,
        HierarchicalChainDistributionAlgorithm $excessDistributor
    ) {
        parent::__construct(
            $transactionManager,
            $settingsProvider,
            $limitCalculator,
            $saleDetailFactory,
            $saleCreator,
            $salesService
        );

        $this->excessDistributor = $excessDistributor;
    }

    /**
     * Get the DistributionType for this strategy
     *
     * @return DistributionType
     */
    protected function getDistributionType(): DistributionType
    {
        return DistributionType::LOCAL_CHAINS;
    }

}
