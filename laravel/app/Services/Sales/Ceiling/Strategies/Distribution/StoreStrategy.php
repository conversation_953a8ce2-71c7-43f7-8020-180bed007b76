<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesDistributionService;
use Illuminate\Support\Facades\Log;

/**
 * Refactored Store Distribution Strategy
 *
 * This strategy uses 90/10 split distribution for the full original quantity
 * when sales exceed the distribution limit. It skips the limited sale creation
 * and distributes the entire quantity using the split algorithm.
 * It follows SOLID principles and uses dependency injection.
 */
class StoreStrategy extends AbstractDistributionStrategy
{
    private ExcessDistributorInterface $excessDistributor;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SettingsProviderInterface   $settingsProvider,
        LimitCalculatorInterface    $limitCalculator,
        SaleDetailFactory           $saleDetailFactory,
        SaleCreator                 $saleCreator,
        SalesDistributionService    $salesService,
        SplitDistributionAlgorithm  $excessDistributor
    ) {
        parent::__construct(
            $transactionManager,
            $settingsProvider,
            $limitCalculator,
            $saleDetailFactory,
            $saleCreator,
            $salesService
        );

        $this->excessDistributor = $excessDistributor;
    }

    protected function validateCeilingSale($ceilingSale): bool
    {
        return isset($ceilingSale->sale_ids) &&
            isset($ceilingSale->number_of_units);
    }

    /**
     * Internal method that performs the actual ceiling sale processing within a transaction
     *
     * @param mixed $ceilingSale
     * @param array $salesContributionBaseOn
     * @return bool
     * @throws \Exception When any step fails to trigger transaction rollback
     */
    public function processCeilingSaleInternal($ceilingSale, array $salesContributionBaseOn): bool
    {
        // Step 1: Validate ceiling sale
        if (!$this->validateCeilingSale($ceilingSale)) {
            Log::warning('StoreStrategy: Ceiling sale validation failed', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
            ]);
            throw new \Exception('Ceiling sale validation failed');
        }


        // Step 2: Get original sale
        $originalSale = $this->getOriginalSale($ceilingSale);
        if (!$originalSale) {
            Log::error('StoreStrategy: Failed to retrieve original sale', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_ids' => $ceilingSale->sale_ids ?? 'unknown'
            ]);
            throw new \Exception('Failed to retrieve original sale');
        }


        // Step 4: Update original sales ceiling status
        if (!$this->updateOriginalSalesCeiling($ceilingSale)) {
            Log::error('StoreStrategy: Failed to update original sales ceiling status', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_ids' => $ceilingSale->sale_ids ?? 'unknown'
            ]);
            throw new \Exception('Failed to update original sales ceiling status');
        }


        // Step 5: Create and distribute full quantity using 90/10 split
        $result = $this->createAndDistributeFullQuantitySale($ceilingSale, $originalSale, $salesContributionBaseOn);

        if (!$result) {
            Log::error('StoreStrategy: Distribution failed, triggering transaction rollback', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
            ]);
            throw new \Exception('Distribution failed - no sale details created or validation failed');
        }

        return true;
    }

    /**
     * Create and distribute sale with full original quantity using 90/10 split distribution
     *
     * This method creates a new sale with the full quantity and attempts to distribute it.
     * Enhanced with comprehensive validation to ensure data integrity. If distribution
     * fails or validation doesn't pass, the method returns false to trigger transaction rollback.
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     * @throws \Exception When sale creation fails to trigger transaction rollback
     */
    protected function createAndDistributeFullQuantitySale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        // Use the full original quantity instead of just the excess
        $fullQuantity = $ceilingSale->number_of_units;


        try {
            $fullQuantitySale = $this->saleCreator->createExcessSale($ceilingSale, $fullQuantity);
            $fullQuantitySale = $this->saleCreator->loadRelationships($fullQuantitySale);
        } catch (\Throwable $e) {
            Log::error('StoreStrategy: Failed to create excess sale', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'error_message' => $e->getMessage()
            ]);
            throw new \Exception('Failed to create excess sale: ' . $e->getMessage());
        }



        $distributionSuccess = $this->excessDistributor->distributeExcessSale(
            $fullQuantitySale,
            $salesContributionBaseOn,
            $originalSale,
            $this->getDistributionType()
        );

        if ($distributionSuccess) {
            // Enhanced validation: Verify sale details were actually created and are valid
            $validationResult = $this->validateDistributionIntegrity($fullQuantitySale, $ceilingSale);

            if ($validationResult['valid']) {

                try {
                    $this->saleCreator->attachMapping($fullQuantitySale, $ceilingSale->mapping_id);
                } catch (\Throwable $e) {
                    Log::error('StoreStrategy: Failed to attach mapping', [
                        'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                        'sale_id' => $fullQuantitySale->id,
                        'error_message' => $e->getMessage()
                    ]);
                    // Don't fail the entire process for mapping attachment failure
                }

                return true;
            } else {
                Log::error('StoreStrategy: Distribution validation failed', [
                    'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                    'sale_id' => $fullQuantitySale->id,
                    'validation_details' => $validationResult
                ]);
                return false;
            }
        } else {
            Log::error('StoreStrategy: Distribution algorithm failed', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_id' => $fullQuantitySale->id,
                'quantity' => $fullQuantitySale->quantity,
                'reason' => 'SplitDistributionAlgorithm returned false'
            ]);
            return false;
        }
    }


    /**
     * Create and distribute excess sale using 90/10 split distribution
     *
     * @deprecated This method is kept for backward compatibility but is no longer used
     * in the Store strategy. Use createAndDistributeFullQuantitySale instead.
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    protected function createAndDistributeExcessSale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        return false;
    }

    /**
     * Get the DistributionType for this strategy
     *
     * @return DistributionType
     */
    protected function getDistributionType(): DistributionType
    {
        return DistributionType::STORES;
    }
}
