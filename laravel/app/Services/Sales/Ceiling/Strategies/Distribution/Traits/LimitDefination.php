<?php


namespace App\Services\Sales\Ceiling\Strategies\Distribution\Traits;


use Illuminate\Support\Facades\Log;

trait LimitDefination {
    /**
     * Calculate excess quantity for distribution
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float
    {
        $className = self::class;
        // Calculate the actual excess above the limit
        $limit = 0;
        $totalUnits = $ceilingSale->number_of_units ?? 0;

        if ($totalUnits > 0) {
            $limit = $ceilingSale->limit ?? 0;
        }

        if ($totalUnits < 0) {
            $limit = $ceilingSale->negative_limit ?? 0;
        }

        $excessQuantity = $totalUnits - $limit;

        Log::debug("$className: Calculated excess quantity", [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'total_units' => $totalUnits,
            'limit' => $limit,
            'excess_quantity' => $excessQuantity,
            'is_positive_units' => $totalUnits > 0,
            'is_negative_units' => $totalUnits < 0,
            'limit_type' => $totalUnits > 0 ? 'positive_limit' : ($totalUnits < 0 ? 'negative_limit' : 'no_limit')
        ]);

        return $excessQuantity;
    }
}
