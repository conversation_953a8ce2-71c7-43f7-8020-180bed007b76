<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Traits;

use App\Sale;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

trait StrategicDetailHandler
{
    protected function validateDetailDistributionIntegrity(Sale $sale, array $details, Collection $divisionsBricks): bool
    {
        $className = self::class;

        if (empty($details)) {
            Log::error("$className: Distribution failed - no details created", [
                'sale_id' => $sale->id,
                'ratios_count' => $divisionsBricks->count(),
                'reason' => 'No distribution ratios resulted in sale details'
            ]);
            return false;
        }

        $totalQuantity = array_sum(array_column($details, 'quantity'));
        $quantityMatch = abs($totalQuantity - $sale->quantity) < 0.01;

        if (!$quantityMatch) {
            Log::error("$className: Distribution failed - quantity mismatch", [
                'sale_id' => $sale->id,
                'expected_quantity' => $sale->quantity,
                'calculated_quantity' => $totalQuantity,
                'difference' => abs($totalQuantity - $sale->quantity)
            ]);
            return false;
        }

        return true;
    }

    protected function insertDetail(Sale $sale, array $details): bool
    {
        $className = self::class;
        try {
            $result = $this->saleDetailFactory->insertDetails($details);
        } catch (\Throwable $e) {
            Log::error("$className: Failed to insert sale details", [
                'sale_id' => $sale->id,
                'details_count' => count($details),
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            return false;
        }

        if (!$result) {
            Log::error("$className: Sale details insertion failed", [
                'sale_id' => $sale->id,
                'details_count' => count($details),
                'reason' => 'SaleDetailFactory::insertDetails returned false'
            ]);
            return false;
        }

        return $result;
    }
}
