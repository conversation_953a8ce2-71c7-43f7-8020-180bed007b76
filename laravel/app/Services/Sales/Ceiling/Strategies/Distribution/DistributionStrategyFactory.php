<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\DivisionType;
use App\Services\Sales\Ceiling\SalesCeilingDetectionService;
use Illuminate\Container\Container;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

/**
 * Factory for creating distribution strategy instances
 *
 * This factory follows the Factory pattern and uses <PERSON><PERSON>'s service container
 * for dependency injection, making it easy to add new strategies.
 */
class DistributionStrategyFactory
{
    private Container $container;
    private array $strategyMap;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->initializeStrategyMap();
    }

    /**
     * Create a distribution strategy instance
     *
     * @param DistributionType $distributionType
     * @return DistributionStrategy
     */
    public function create(DistributionType $distributionType): DistributionStrategy
    {
        if (!isset($this->strategyMap[$distributionType->value])) {
            throw new InvalidArgumentException("Unknown distribution type: {$distributionType->getName()}");
        }

        $strategyClass = $this->strategyMap[$distributionType->value];

        $result = tryCatch(function () use ($strategyClass) {
            return $this->container->make($strategyClass);
        });

        if ($result->isSuccess()) {
            return $result->getValue();
        }

        Log::error('Failed to create distribution strategy', [
            'error' => $result->getError()->getMessage()
        ]);
        throw $result->getError();
    }

    /**
     * Create strategy by name
     *
     * @param string $strategyName
     * @return DistributionStrategy
     * @throws InvalidArgumentException|BindingResolutionException
     */
    public function createByName(string $strategyName): DistributionStrategy
    {
        $strategyClass = $this->getStrategyClassByName($strategyName);

        if (!$strategyClass) {
            throw new InvalidArgumentException("Unknown strategy name: {$strategyName}");
        }

        return $this->container->make($strategyClass);
    }

    /**
     * Register a new strategy
     *
     * @param int $distributionType
     * @param string $strategyClass
     * @return void
     */
    public function registerStrategy(DistributionType $distributionType, string $strategyClass): void
    {
        if (!is_subclass_of($strategyClass, DistributionStrategy::class)) {
            throw new InvalidArgumentException(
                "Strategy class must implement DistributionStrategy interface"
            );
        }

        $this->strategyMap[$distributionType->value] = $strategyClass;
    }

    /**
     * Get all available distribution types
     *
     * @return array
     */
    public function getAvailableTypes(): array
    {
        return array_keys($this->strategyMap);
    }

    /**
     * Check if a distribution type is supported
     *
     * @param DistributionType $distributionType
     * @return bool
     */
    public function supports(DistributionType $distributionType): bool
    {
        return isset($this->strategyMap[$distributionType->value]);
    }

    /**
     * Get strategy information including algorithm details
     *
     * @return array
     */
    public function getStrategyInformation(): array
    {
        return [
            DistributionType::PRIVATE_PHARMACY->value => [
                'name' => 'Private Pharmacy Strategy',
                'algorithm' => 'Simple Distribution (100%)',
                'description' => 'Distributes 100% of excess sales using normal distribution ratios',
                'class' => PrivatePharmacyStrategy::class
            ],
            DistributionType::STORES->value => [
                'name' => 'Store Strategy',
                'algorithm' => 'Full Quantity Split Distribution (90/10)',
                'description' => 'Distributes the full original quantity using 90% primary and 10% secondary allocation, skipping limited sale creation',
                'class' => StoreStrategy::class
            ],
            DistributionType::LOCAL_CHAINS->value => [
                'name' => 'Local Chain Strategy',
                'algorithm' => 'Hierarchical Chain Distribution (60/25/15)',
                'description' => 'Distributes excess sales across three tiers: 60% headquarters, 25% regional, 15% stores',
                'class' => LocalChainStrategy::class
            ]
        ];
    }

    /**
     * Initialize the strategy mapping
     *
     * @return void
     */
    private function initializeStrategyMap(): void
    {
        $this->strategyMap = [
            DistributionType::PRIVATE_PHARMACY->value => PrivatePharmacyStrategy::class,
            DistributionType::STORES->value => StoreStrategy::class,
            DistributionType::LOCAL_CHAINS->value => LocalChainStrategy::class,
        ];
    }

    /**
     * Get strategy class by name
     *
     * @param string $strategyName
     * @return string|null
     */
    private function getStrategyClassByName(string $strategyName): ?string
    {
        $nameMap = [
            'private_pharmacy' => PrivatePharmacyStrategy::class,
            'store' => StoreStrategy::class,
            'local_chain' => LocalChainStrategy::class,
        ];

        return $nameMap[strtolower($strategyName)] ?? null;
    }
}
