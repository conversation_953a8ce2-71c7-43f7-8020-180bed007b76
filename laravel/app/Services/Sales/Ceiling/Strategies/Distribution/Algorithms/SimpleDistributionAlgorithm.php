<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\Traits\LimitDefination;
use App\Services\Sales\Ceiling\Strategies\Distribution\Traits\StrategicDetailHandler;
use App\Services\SalesDistributionService;
use Illuminate\Support\Facades\Log;

/**
 * Simple distribution algorithm that distributes 100% of excess sales
 * using normal distribution ratios (used by PrivatePharmacyStrategy)
 */
class SimpleDistributionAlgorithm implements ExcessDistributorInterface
{

    use StrategicDetailHandler;
    use LimitDefination;


    public function __construct(
        private readonly SaleDetailFactory $saleDetailFactory,
        private readonly SalesServiceFactoryInterface $salesServiceFactory
    ) {
    }

    /**
     * Distribute excess sale using simple 100% distribution
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @param DistributionType|null $distributionType
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn,?Sale $originalSale = null, ?DistributionType $distributionType = null): bool
    {
        // Create SalesService with the appropriate DistributionType using factory
        $salesService = $this->salesServiceFactory->createNormal($distributionType);

        $divisionsBricks = $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn
            );

        $details = $this->saleDetailFactory->createDetailsFromRatios($sale, $divisionsBricks->toArray());

        // Validate distribution integrity before attempting insertion
        if($this->validateDetailDistributionIntegrity($sale,$details,$divisionsBricks)){
            return $this->insertDetail($sale,$details);
        }

        return false;
    }

}
