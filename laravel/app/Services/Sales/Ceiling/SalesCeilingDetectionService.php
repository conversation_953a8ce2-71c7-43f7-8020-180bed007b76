<?php

namespace App\Services\Sales\Ceiling;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SalesCeilingDetectionService
{

    public function __construct(
        private readonly Sale $saleModel
    )
    {
    }

    /**
     * Query ceiling sales data for a given period and criteria
     */
    public function queryCeilingSales(
        DistributionType $type,
        string           $from,
        string           $to,
        array            $productIds = [],
        array            $distributorIds = []
    ): Collection
    {
        $fromFirstOfMonth = Carbon::parse($from)->firstOfMonth()->format('Y-m-d');
        $toDate = Carbon::parse($to)->format('Y-m-d');
        $period = CarbonPeriod::create($fromFirstOfMonth, '1 Month', $toDate);


        // For multiple months, build a UNION ALL query
        return $this->saleModel->newQuery()
            ->unionFromCallback($period, function ($dateString) use ($productIds, $distributorIds, $type) {
                $date = Carbon::parse($dateString)->firstOfMonth()->format('Y-m-d');
                return $this->buildMonthlyQuery($date, $productIds, $distributorIds, $type);
            })->get();
    }


    /**
     * Build a query for a specific month without executing it
     * Used for building UNION queries
     */
    private function buildMonthlyQuery(
        string           $date,
        array            $productIds,
        array            $distributorIds,
        DistributionType $type,
    ): Builder
    {
        $selection = $this->getQuerySelection();

        $query = $this->saleModel->newQuery()
            ->select($selection)
            ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
            ->join("mappings", function ($join) use ($type) {
                $join->on('mapping_sale.mapping_id', 'mappings.id');
                if ($type === DistributionType::PRIVATE_PHARMACY) {
                    $join->where(function ($q) {
                        $q->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::STORES->value)
                            ->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::LOCAL_CHAINS->value)
                            ->orWhere('mappings.unified_pharmacy_type_id', null);
                    });

                    return;
                }
                $join->where('mappings.unified_pharmacy_type_id', $type->value);

            })
            ->join('products', 'sales.product_id', 'products.id')
            ->leftJoin('distributors', 'sales.distributor_id', 'distributors.id');

        // Use LEFT JOIN for product_ceilings when distribution type is STORES, INNER JOIN for others
        if ($type === DistributionType::STORES) {
            $query->leftJoin('product_ceilings', function ($join)use($date) {
                $join->on('product_ceilings.product_id', 'sales.product_id')
                    ->where('product_ceilings.from_date', '<=', $date)
                    ->where(
                        fn($q) => $q
                            ->where('product_ceilings.to_date', '>', $date)
                            ->orWhere('product_ceilings.to_date', null)
                    );
            });
        } else {
            $query->join('product_ceilings', function ($join)use($date) {
                $join->on('product_ceilings.product_id', 'sales.product_id')
                    ->where('product_ceilings.from_date', '<=', $date)
                    ->where(
                        fn($q) => $q
                            ->where('product_ceilings.to_date', '>', $date)
                            ->orWhere('product_ceilings.to_date', null)
                    );
            });
        }

        $query
            ->groupBy($this->getGroupByColumns())
            ->where('date', $date)
            ->whereCeiling(Ceiling::BELOW)
            ->where("mappings.exception", false);


        $this->applyCeilingConditions($query, $type);
        $this->applyFilters($query, $productIds, $distributorIds);

        return $query;
    }

    /**
     * Get the selection columns for the query
     */
    private function getQuerySelection(): array
    {
        return [
            'products.id',
            'products.name',
            'mappings.name as pharmacy',
            'mappings.id as mapping_id',
            'sales.distributor_id',
            'sales.date',
            'distributors.name as distributor',
            'product_ceilings.units as limit',
            'product_ceilings.negative_units as negative_limit',
            DB::raw('Sum(crm_sales.quantity) as number_of_units'),
            DB::raw('Sum(crm_sales.value) as number_of_values'),
            DB::raw('Sum(crm_sales.bonus) as number_of_bonus'),
            DB::raw('group_concat(crm_sales.id) as sale_ids')
        ];
    }

    /**
     * Get the group by columns
     */
    private function getGroupByColumns(): array
    {
        return [
            'products.id',
            'products.name',
            'sales.distributor_id',
            'distributors.name',
            'sales.date',
            'mappings.id',
            'product_ceilings.units',
            'product_ceilings.negative_units'
        ];
    }

    /**
     * Apply ceiling conditions to the query
     */
    private function applyCeilingConditions(Builder $query, DistributionType $type): void
    {
        if ($type !== DistributionType::STORES) {
            // For STORES with LEFT JOIN, handle null ceiling values
            // OR ABS(SUM(crm_sales.quantity)) > crm_product_ceilings.units or ABS(SUM(crm_sales.quantity)) < crm_product_ceilings.units
//            $query->havingRaw('crm_product_ceilings.units IS NULL or crm_product_ceilings.units IS NOT NULL');
//        } else {
            // For other types with INNER JOIN, use original logic
            $query->havingRaw('ABS(SUM(crm_sales.quantity)) > crm_product_ceilings.units');
        }
    }

    /**
     * Apply filters to the query
     */
    private function applyFilters($query, array $productIds, array $distributorIds): void
    {
        if (!empty($distributorIds)) {
            $query->whereIn('sales.distributor_id', $distributorIds);
        }

        if (!empty($productIds)) {
            $query->whereIn('sales.product_id', $productIds);
        }
    }
}
