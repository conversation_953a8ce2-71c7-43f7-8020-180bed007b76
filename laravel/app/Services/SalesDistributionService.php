<?php

namespace App\Services;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesDistributionService
{
    private readonly array $groupBy;

    private function __construct(
        private readonly SaleDistribution  $saleDistribution,
        private readonly ?DistributionType $distributionType = null
    )
    {

        $this->groupBy = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
        ];

        Log::debug('SalesService: Initialized', [
            'sale_distribution' => $saleDistribution->name,
            'distribution_type' => $distributionType?->value,
            'group_by_count' => count($this->groupBy),
            'context' => 'SERVICE_INITIALIZATION'
        ]);
    }

    public static function make(SaleDistribution $saleDistribution, ?DistributionType $distributionType = null): static
    {
        return new static($saleDistribution, $distributionType);
    }

    private function generateCacheKey(array $parameters): string
    {
        $cacheKeyData = [
            'function' => 'getRatiosForDistribution',
            'params_hash' => md5(serialize($parameters)),
            'distribution_type' => $this->distributionType?->value,
            'sale_distribution' => $this->saleDistribution->name,
        ];

        return 'sales_dist_' . md5(serialize($cacheKeyData));
    }

    public function getRatiosForDistribution(string $date, int $product, array $distributorIds, array $divisionIds = []): Collection
    {
        if (empty($distributorIds)) {
            Log::warning('SalesService: Empty distributor IDs provided');
            return collect();
        }

        // Generate a cache key for this specific query
//        $cacheKey = $this->generateCacheKey([
//            'date' => $date,
//            'product' => $product,
//            'distributorIds' => $distributorIds,
//            'divisionIds' => $divisionIds
//        ]);
//
//        // Use cache for 1 hour
//        return Cache::remember($cacheKey, 60 * 60, function () use ($date, $product, $distributorIds, $divisionIds) {
            $date = Carbon::parse($date);

            try {
                // Simplified query to test basic functionality
                $query = Sale::query();
                $query->select([
                    "sales_details.div_id",
                    "sales_details.line_id",
                    "sales_details.brick_id",
                    DB::raw('SUM(crm_sales_details.quantity) AS total_quantity'),
                    DB::raw('COUNT(*) AS record_count'),
                    DB::raw('group_concat(crm_sales_details.id) AS sale_detail_id'),
                ]);

                $this->buildJoins($query, $date, $product, $divisionIds);
                $ceilings = $this->getCeiling();

                $query
                    ->whereYear("sales.date", $date->year)
                    ->whereMonth("sales.date", $date->month)
                    ->where("sales.product_id", $product)
                    ->whereIn("sales.distributor_id", $distributorIds)
                    ->whereIn("sales.ceiling", $ceilings)
                    ->groupBy($this->groupBy);

                $result = $query->get();


                if ($result->count() > 0) {
                    // If basic query works, let's add percentage calculation
                    return $this->addPercentageCalculation($result);
                }

                return $result;

            } catch (\Exception $e) {
                Log::error('Approach 3 failed', ['error' => $e->getMessage()]);
                return collect();
            }
//        });
    }


    private function addPercentageCalculation($basicResults): Collection
    {
        try {
            $totalQuantity = $basicResults->sum("total_quantity");

            // Add percentage to each result
            return $basicResults->map(function ($item) use ($totalQuantity) {
                $item->percentage = $totalQuantity != 0 ?
                    ($item->total_quantity / $totalQuantity) : 0;

                $item->total_details = $totalQuantity;
                return $item;
            });

        } catch (\Exception $e) {
            Log::error('Failed to add percentage calculation', ['error' => $e->getMessage()]);
            return $basicResults;
        }
    }

    // DEBUGGING: Let's also add a method to check basic data existence
    public function debugDataExistence(string $date, int $product, array $distributorIds): array
    {
        $date = Carbon::parse($date);

        $basicSalesCount = Sale::where('product_id', $product)
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereIn('distributor_id', $distributorIds)
            ->count();

        $salesWithDetailsCount = Sale::where('product_id', $product)
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereIn('distributor_id', $distributorIds)
            ->whereHas('details')
            ->count();

        $ceilings = $this->getCeiling();
        $salesWithCeilingCount = Sale::where('product_id', $product)
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereIn('distributor_id', $distributorIds)
            ->whereIn('ceiling', array_map(fn($c) => $c->value, $ceilings))
            ->count();

        return [
            'basic_sales_count' => $basicSalesCount,
            'sales_with_details_count' => $salesWithDetailsCount,
            'sales_with_ceiling_count' => $salesWithCeilingCount,
            'ceilings_used' => array_map(fn($c) => $c->value, $ceilings),
            'date_year' => $date->year,
            'date_month' => $date->month,
            'product_id' => $product,
            'distributor_ids' => $distributorIds
        ];
    }

    private function buildJoins($query, Carbon $date, int $product, array $divisionIds): void
    {
        $query
            ->join("sales_details", "sales.id", "sales_details.sale_id")
            ->join("line_products", function ($join) use ($date, $product) {
                $join
                    ->on("sales.product_id", "=", "line_products.product_id")
                    ->where("line_products.product_id", $product)
                    ->whereColumn("line_products.line_id", "sales_details.line_id")
                    ->where("line_products.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_products.to_date", ">", $date)
                            ->orWhereNull("line_products.to_date")
                    );
            })
            ->join("line_divisions", function ($join) use ($date, $divisionIds) {
                $join
                    ->on("sales_details.div_id", "=", "line_divisions.id")
                    ->whereColumn("line_divisions.line_id", "line_products.line_id")
                    ->where("line_divisions.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_divisions.to_date", ">", $date)
                            ->orWhereNull("line_divisions.to_date")
                    );

                if (!empty($divisionIds)) {
                    $join->whereIn('line_divisions.id', $divisionIds);
                }
            });

        $query
            ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
            ->join("mappings", function ($join) {
                $join->on('mapping_sale.mapping_id', 'mappings.id');
                if ($this->distributionType === DistributionType::STORES) {
                    $join->whereIn('mappings.unified_pharmacy_type_id', [DistributionType::PRIVATE_PHARMACY->value, DistributionType::LOCAL_CHAINS->value]);
                } else if ($this->distributionType) {
                    $join->where('mappings.unified_pharmacy_type_id', $this->distributionType->value);
                }
            });

        if ($this->saleDistribution == SaleDistribution::NORMAL) {
            if ($this->distributionType !== DistributionType::STORES) {
                $query->join("product_ceilings", function ($join) use ($date) {
                    $join
                        ->on("product_ceilings.product_id", "=", "sales.product_id")
                        ->where("product_ceilings.from_date", "<=", $date)
                        ->where(
                            fn($q) => $q
                                ->where("product_ceilings.to_date", ">", $date)
                                ->orWhereNull("product_ceilings.to_date")
                        )
                        ->whereRaw('ABS(crm_sales.quantity) <= crm_product_ceilings.units');
                });
            }
        }
    }

    private function getCeiling(): array
    {
        return match ($this->saleDistribution) {
            SaleDistribution::NORMAL => [Ceiling::BELOW],
            SaleDistribution::DIRECT => [Ceiling::BELOW, Ceiling::DISTRIBUTED]
        };
    }
}
