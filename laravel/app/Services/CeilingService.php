<?php

namespace App\Services;

use App\Jobs\ProcessCallbackJob;
use App\Sale;
use App\SaleDetail;
use App\SalesSetting;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDistribution;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\SerializableClosure\Exceptions\PhpVersionNotSupportedException;
use Laravel\SerializableClosure\SerializableClosure;

class CeilingService
{
    public static function query(
        string $from,
        string $to,
        bool $positive = true,
        array $productIds = [],
        array $distributorIds = []
    ):Collection {
        $selection = [
            "products.id",
            "products.name",
            "mappings.name as pharmacy",
            "mappings.id as mapping_id",
            "sales.distributor_id",
            "sales.date",
            "distributors.name as distributor",
            "product_ceilings.units as limit",
            "product_ceilings.negative_units as negative_limit",
            DB::raw("SUM(crm_sales.quantity) as number_of_units"),
            DB::raw("SUM(crm_sales.value) as number_of_values"),
            DB::raw("SUM(crm_sales.bonus) as number_of_bonus"),
            DB::raw("GROUP_CONCAT(crm_sales.id) as sale_ids")
        ];

        $query = Sale::select($selection)
            ->join("mapping_sale", "mapping_sale.sale_id", "sales.id")
            ->join("mappings", "mapping_sale.mapping_id", "mappings.id")
            ->join("products", "sales.product_id", "products.id")
            ->leftJoin("distributors", "sales.distributor_id", "distributors.id")
            ->join("product_ceilings", function ($join) {
                $join
                    ->on("product_ceilings.product_id", "sales.product_id")
                    ->where("product_ceilings.from_date", "<=", now())
                    ->where(
                        fn($q) => $q
                            ->where("product_ceilings.to_date", ">", now())
                            ->orWhere("product_ceilings.to_date", null)
                    );
            })
            ->where("mappings.exception", false)
            ->whereBetween(DB::raw("DATE_FORMAT(crm_sales.date, '%Y-%m-01')"), [
                $from,
                $to
            ])
            ->whereCeiling(Ceiling::BELOW)
            ->groupBy([
                "products.id",
                "products.name",
                "sales.distributor_id",
                "distributors.name",
                // DB::raw("DATE_FORMAT(sales.date, '%Y-%m-01')"),
                "sales.date",
                "mappings.id",
                "product_ceilings.units",
                "product_ceilings.negative_units"
            ]);

        if ($positive) {
            $query->havingRaw("SUM(crm_sales.quantity) > crm_product_ceilings.units");
        } else {
            $query
                ->havingRaw(
                    "SUM(crm_sales.quantity) < crm_product_ceilings.negative_units AND SUM(crm_sales.quantity) <> crm_product_ceilings.negative_units"
                )
                ->where("sales.quantity", "<", 0);
        }

        if (!empty($distributorIds)) {
            $query->whereIntegerInRaw("sales.distributor_id", $distributorIds);
        }

        if (!empty($productIds)) {
            $query->whereIntegerInRaw("sales.product_id", $productIds);
        }

        return $query->get();
    }

    public static function recalcDistributeDiff(Collection $CeilingSales, QueueLoadBalancingService $balancingService): void
    {
        $salesIdsToRedistribute = $CeilingSales->pluck("sale_ids")
            ->map(function ($item) {
                return explode(",", $item);
            })
            ->flatten();
        set_time_limit(300);
        DB::beginTransaction();
        try {
            $salesContribution = SalesSetting::where('key', 'sales_contribution_base_on')->value('value');
            if (isNullable($salesContribution)) {
                DB::rollBack();
                return;
            }
            $salesContributionBaseOn = explode(',', $salesContribution);

            Sale::whereIn("id", $salesIdsToRedistribute)
                ->update([
                    "ceiling" => Ceiling::ABOVE
                ]);

            foreach ($CeilingSales as $ceilingSale) {

                static::launchOldDistributionJob($ceilingSale);

                // implement distribution for sales

                $sale = Sale::create([
                    'quantity' => $ceilingSale->number_of_units - $ceilingSale->limit,
                    'value' => 0,
                    'bonus' => 0,
                    'region' => 0,
                    'distributor_id' => $ceilingSale->distributor_id,
                    'product_id' => $ceilingSale->id,
                    'date' => $ceilingSale->date,
                    "ceiling" => Ceiling::DISTRIBUTED
                ]);

                $sale->load(["product:id", "product.lines:id"]);

                static::launchDistributionJob($sale, $salesContributionBaseOn, $balancingService);

                $sale->mappings()->attach($ceilingSale->mapping_id);
            }

            DB::commit();
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            DB::rollBack();
        }
    }

    public static function recalcDistributeAll(Collection $CeilingSales, QueueLoadBalancingService $balancingService): void
    {
        $salesIdsToRedistribute = $CeilingSales->pluck("sale_ids")
            ->map(function ($item) {
                return explode(",", $item);
            })
            ->flatten();

        DB::beginTransaction();
        try {
            Sale::whereIn("id", $salesIdsToRedistribute)
                ->update([
                    "ceiling" => Ceiling::ABOVE
                ]);

            SaleDetail::whereIn("sale_id", $salesIdsToRedistribute)
                ->delete();

            $salesContribution = SalesSetting::where('key', 'sales_contribution_base_on')->value('value');

            if (isNullable($salesContribution)) {
                DB::rollBack();
                return;
            }
            $salesContributionBaseOn = explode(',', $salesContribution);


            Sale::whereIn("id", $salesIdsToRedistribute)
                ->with([
                    "product:id",
                    "product.lines:id"
                ])
                ->chunk(50, function ($sales) use ($salesContributionBaseOn, $balancingService) {
                    foreach ($sales as $sale) {
                        static::launchDistributionJob($sale, $salesContributionBaseOn, $balancingService);
                    }
                });
            Log::info($salesIdsToRedistribute);
            Sale::whereIn("id", $salesIdsToRedistribute)
                ->update([
                    "ceiling" => Ceiling::DISTRIBUTED
                ]);
            DB::commit();
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            DB::rollBack();
        }
    }

    /**
     * @throws PhpVersionNotSupportedException
     */
    private static function launchDistributionJob(Sale $sale, array $salesContributionBaseOn, QueueLoadBalancingService $balancingService): void
    {
        $divisionsBricks = SalesDistributionService::make(SaleDistribution::NORMAL)->getRatiosForDistribution(
            $sale->date,
            $sale->product_id,
            $salesContributionBaseOn
        );


        foreach ($divisionsBricks as $divisionBrick) {
            $queue = $balancingService->currentQueue();

            $creationClosure = new SerializableClosure(
                function () use ($sale, $divisionBrick) {
                    SaleDetail::create([
                        'sale_id' => $sale->id,
                        'div_id' => $divisionBrick->div_id,
                        'brick_id' => $divisionBrick->brick_id,
                        'date' => $sale->date,
                        'quantity' => $divisionBrick->percentage * $sale->quantity,
                        'bonus' => $divisionBrick->percentage * $sale->bonus,
                        'value' => $divisionBrick->percentage * $sale->value,
                        'file_id' => $sale->file_id,
                    ]);
                }
            );

            $processClosure = new SerializableClosure(
                function () use ($creationClosure, $queue) {
                    ProcessCallbackJob::dispatch(
                        serialize(
                            $creationClosure
                        )
                    )->onQueue($queue);
                }
            );

            $balancingService->currentProcessBatch()->add([
                new ProcessCallbackJob(
                    serialize($processClosure)
                )
            ]);

            $balancingService->next();
        }
    }

    /**
     * @throws PhpVersionNotSupportedException
     */
    private static function launchOldDistributionJob($ceilingSale): void//, QueueLoadBalancingService $balancingService): void
    {
//        $queue = $balancingService->currentQueue();

        $id = explode(",", $ceilingSale->sale_ids)[0];

        $ceilingData = [
            "limit" => $ceilingSale->limit,
            "distributor_id" => $ceilingSale->distributor_id,
            "id" => $ceilingSale->id,
            "date" => $ceilingSale->date,
            "mapping_id" => $ceilingSale->mapping_id,
        ];

        Log::info(collect($ceilingData));

//        $creationClosure = new SerializableClosure(function () use ($id, $ceilingData) {
        $oldSale = Sale::whereId($id)
            ->with("details")
            ->first();
        Log::info($oldSale);

        $details = [];

        $userSaleLimited = Sale::create([
            'quantity' => $ceilingData["limit"],
            'value' => 0,
            'bonus' => 0,
            'region' => 0,
            'distributor_id' => $ceilingData["distributor_id"],
            'product_id' => $ceilingData["id"],
            'date' => $ceilingData["date"],
            "file_id" => $oldSale->file_id
        ]);

        $userSaleLimited->mappings()->attach($ceilingData["mapping_id"]);

        foreach ($oldSale->details as $saleDetail) {
            $details[] = [
                'div_id' => $saleDetail->div_id,
                'brick_id' => $saleDetail->brick_id,
                'quantity' => $userSaleLimited->quantity * ($saleDetail->quantity / $oldSale->quantity),
                'bonus' => $userSaleLimited->bonus * ($saleDetail->quantity / $oldSale->quantity),
                'value' => $userSaleLimited->value * ($saleDetail->quantity / $oldSale->quantity),
                "date" => $ceilingData["date"],
                'sale_id' => $userSaleLimited->id,
                'file_id' => $saleDetail->file_id,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        SaleDetail::insert($details);
//        });

//        $processClosure = new SerializableClosure(
//            function () use ($creationClosure, $queue) {
//                ProcessCallbackJob::dispatch(
//                    serialize(
//                        $creationClosure
//                    )
//                )->onQueue($queue);
//            }
//        );

//        $balancingService->currentProcessBatch()->add([
//            new ProcessCallbackJob(
//                serialize($processClosure)
//            )
//        ]);

//        $balancingService->next();
    }
}
