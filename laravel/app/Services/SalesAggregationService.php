<?php

namespace App\Services;

use App\Services\Enums\Ceiling;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class SalesAggregationService
{

    private CarbonPeriod $period;

    public function __construct(private readonly string $from, private readonly string $to)
    {
        $this->initializeDates();
    }

    public function initializeDates(): void
    {
        $this->period = CarbonPeriod::create($this->from, '1 month', $this->to);
    }

    /**
     * MAIN ISSUE FIX: The problem is likely in your JOINs causing record multiplication
     * Solution: Pre-aggregate the data before joining with lookup tables
     */
    public function getOptimizedSalesAggregation(
        array $lines,
        array $productIds,
        array $distributors,
        array $belowDivisions,
        array $types,
        bool $isSalesPerDistributor = false
    ) {

        $dates = [];
        foreach ($this->period as $date) {
            $dates[] = $date;
        }

        // Step 1: First get the core sales data with minimal joins
        $coreQuery = $this->buildCoreSalesQuery($productIds, $belowDivisions, $dates);

        // Step 2: Build the final query with proper aggregation
        return $this->buildFinalAggregatedQuery(
            $coreQuery,
            $lines,
            $distributors,
            $types,
            $isSalesPerDistributor
        );
    }

    /**
     * Build core sales query with minimal joins to avoid multiplication
     */
    private function buildCoreSalesQuery(array $productIds, array $belowDivisions, array $dates): Builder
    {
        // Pre-aggregate sales details to avoid multiplication in joins
        return DB::table('sales as sales')
            ->select([
                'sales.id as sale_id',
                'sales.distributor_id',
                'sales.product_id',
                'sales.ceiling',
                'sales.value as sale_value',
                'sales.sale_ids',
                'sales_details.id as detail_id',
                'sales_details.div_id',
                'sales_details.brick_id',
                'sales_details.date',
                'sales_details.quantity',
                'sales_details.bonus',
                'sales_details.value as detail_value',
                'sales_details.ratio'
            ])
            ->join('sales_details', function($join) use ($belowDivisions, $dates) {
                $join->on('sales.id', '=', 'sales_details.sale_id')
                    ->whereIn('sales_details.div_id', $belowDivisions)
                    ->whereIn('sales_details.date', $dates);
            })
            ->whereIn('sales.product_id', $productIds);
//            ->where('sales.deleted_at', null);
    }

    /**
     * Fixed version of your selections with proper aggregation
     */
    public function getFixedSelectionsForDetails(): array
    {
        $product_avg_price = $this->getAvgProductPriceQuery();

        return [
            DB::raw("GROUP_CONCAT(DISTINCT crm_core_sales.sale_id) as id"),
            DB::raw("'' as detail_id"),
            'lines.name as line',
            'line_divisions.name as division',
            'core_sales.div_id',
            DB::raw('IFNULL(crm_core_sales.distributor_id,"") as distributor_id'),
            DB::raw('IFNULL(crm_distributors.name,"") AS distributor'),
            DB::raw("'' as brick"),
            DB::raw("'' as brick_id"),
            DB::raw('DATE_FORMAT(crm_core_sales.date, "%Y-%m-%d") as date'),
            DB::raw('MAX(IFNULL(crm_higher.fullname, "")) as manager'),

            // Fixed employee query - use ANY_VALUE to avoid grouping issues
            DB::raw("ANY_VALUE((SELECT IFNULL(crm_users.fullname, '')
                  FROM crm_users
                  JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                  WHERE crm_line_users_divisions.line_division_id = crm_core_sales.div_id
                    AND crm_line_users_divisions.deleted_at IS NULL
                    AND crm_users.deleted_at IS NULL
                    AND crm_line_users_divisions.from_date <= crm_core_sales.date
                    AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_core_sales.date)
                  LIMIT 1)) as employee"
            ),

            DB::raw("ANY_VALUE((SELECT IFNULL(crm_users.emp_code, '')
                  FROM crm_users
                  JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                  WHERE crm_line_users_divisions.line_division_id = crm_core_sales.div_id
                    AND crm_line_users_divisions.deleted_at IS NULL
                    AND crm_users.deleted_at IS NULL
                    AND crm_line_users_divisions.from_date <= crm_core_sales.date
                    AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_core_sales.date)
                  LIMIT 1)) as emp_code"
            ),

            DB::raw('MAX(crm_sales_types.name) as type'),
            DB::raw("'BULK' AS name"),
            DB::raw("'' AS code"),
            DB::raw("'' AS pharmacy_type"),
            DB::raw("'DISTRIBUTED' as distribution_type"),
            'core_sales.product_id',
            'products.name as product',
            DB::raw('MAX(crm_division_types.color) as color'),

            // CRITICAL FIX: Use SUM without FORMAT in subquery, format only in final result
            DB::raw('CAST(SUM(crm_core_sales.quantity) AS DECIMAL(15,2)) as units'),
            DB::raw("CAST(MAX($product_avg_price) AS DECIMAL(15,2)) as pro_price"),
            DB::raw('CAST(SUM(crm_core_sales.bonus) AS DECIMAL(15,2)) as bonus'),

            // Fixed value calculation
            DB::raw("CAST(SUM(
                        CASE
                            WHEN crm_core_sales.sale_value > 0 THEN crm_core_sales.detail_value
                            ELSE ($product_avg_price * crm_core_sales.quantity)
                        END
                    ) AS DECIMAL(15,2)) as value"
            ),
            DB::raw('CAST(SUM(crm_core_sales.ratio) AS DECIMAL(15,8)) as ratio'),
            DB::raw("GROUP_CONCAT(DISTINCT crm_core_sales.sale_ids) AS referenceId"),
        ];
    }

    /**
     * Completely rewritten aggregation method to fix multiplication issues
     */
    public function buildFinalAggregatedQuery(
        $coreQuery,
        array $lines,
        array $distributors,
        array $types,
        bool $isSalesPerDistributor
    ): Builder
    {
        // Convert core query to a subquery
        $coreSubquery = DB::query()->fromSub($coreQuery, 'core_sales');

        // Now build the main query with proper joins
        $query = DB::table(DB::raw("({$coreSubquery->toSql()}) as crm_core_sales"))
            ->mergeBindings($coreSubquery)
            ->select($this->getFixedSelectionsForDetails())

            // Join with products
            ->join('products', 'core_sales.product_id', '=', 'products.id')

            // Join with divisions
            ->join('line_divisions', 'core_sales.div_id', '=', 'line_divisions.id')
            ->join('lines', 'line_divisions.line_id', '=', 'lines.id')

            // Optional distributor join
            ->leftJoin('distributors', 'core_sales.distributor_id', '=', 'distributors.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', '=', 'division_types.id')

            // Add mapping joins only if needed
            ->leftJoin('mapping_sale', 'core_sales.sale_id', '=', 'mapping_sale.sale_id')
            ->leftJoin('mappings', function($join) use ($lines, $distributors, $types) {
                $join->on('mapping_sale.mapping_id', '=', 'mappings.id')
                    ->whereIn('mappings.mapping_type_id', $types)
                    ->where(function($q) use ($lines) {
                        $q->whereIn('mappings.line_id', $lines)
                            ->orWhereNull('mappings.line_id');
                    })
                    ->where(function($q) use ($distributors) {
                        $q->whereIn('mappings.distributor_id', $distributors)
                            ->orWhereNull('mappings.distributor_id');
                    });
            })
            ->leftJoin('sales_types', 'mappings.mapping_type_id', '=', 'sales_types.id')

            // Manager hierarchy joins (simplified)
            ->leftJoin('line_users_divisions as manager_divisions', function($join) {
                $join->on('line_divisions.id', '=', 'manager_divisions.line_division_id')
                    ->whereNull('manager_divisions.deleted_at')
                    ->where('manager_divisions.from_date', '<=', $this->to)
                    ->where(function($query) {
                        $query->whereNull('manager_divisions.to_date')
                            ->orWhere('manager_divisions.to_date', '>=', $this->from);
                    });
            })
            ->leftJoin('users as higher', 'manager_divisions.user_id', 'higher.id')

            // Apply filters
            ->whereIn('lines.id', $lines)
            ->where('core_sales.ceiling', '2') // DISTRIBUTED

            // Group by the essential fields only
            ->groupBy([
                'core_sales.distributor_id',
                'distributors.name',
                'core_sales.div_id',
                'line_divisions.name',
                'lines.id',
                'lines.name',
                'core_sales.date',
                'core_sales.product_id',
                'products.name'
            ])

            ->orderBy('core_sales.date')
            ->orderBy('products.name');

        return $query;
    }

    /**
     * Alternative approach: Direct aggregation without complex joins
     */
    public function getSimplifiedAggregation(
        array $productIds,
        array $divisionIds,
        array $dates,
        Ceiling $distributionType = Ceiling::DISTRIBUTED
    ) {
        $ceilingValue = $distributionType->value;

        return DB::table('sales as s')
            ->select([
                's.distributor_id',
                'd.name as distributor_name',
                's.product_id',
                'p.name as product_name',
                'sd.div_id',
                'ld.name as division_name',
                'sd.date',
                DB::raw('SUM(crm_sd.quantity) as total_units'),
                DB::raw('SUM(crm_sd.value) as total_value'),
                DB::raw('SUM(crm_sd.bonus) as total_bonus'),
                DB::raw('SUM(crm_sd.ratio) as total_ratio'),
                DB::raw('COUNT(DISTINCT crm_s.id) as sales_count')
            ])
            ->join('sales_details as sd', 's.id', '=', 'sd.sale_id')
            ->join('products as p', 's.product_id', '=', 'p.id')
            ->join('line_divisions as ld', 'sd.div_id', '=', 'ld.id')
            ->leftJoin('distributors as d', 's.distributor_id', '=', 'd.id')
            ->whereIn('s.product_id', $productIds)
            ->whereIn('sd.div_id', $divisionIds)
            ->whereIn('sd.date', $dates)
            ->where('s.ceiling', $ceilingValue)
//            ->whereNull('s.deleted_at')
            ->groupBy([
                's.distributor_id', 'd.name', 's.product_id', 'p.name',
                'sd.div_id', 'ld.name', 'sd.date'
            ])
            ->orderBy('sd.date')
            ->orderBy('p.name');
    }

    /**
     * Debug query to check for multiplication issues
     */
    public function debugQuantityIssues(array $productIds, array $divisionIds, array $dates)
    {
        // Check if there are duplicate records in sales_details
        $duplicateCheck = DB::table('crm_sales_details')
            ->select('sale_id', 'div_id', 'date', DB::raw('COUNT(*) as count'))
            ->whereIn('div_id', $divisionIds)
            ->whereIn('date', $dates)
            ->groupBy('sale_id', 'div_id', 'date')
            ->having('count', '>', 1)
            ->get();

        if ($duplicateCheck->isNotEmpty()) {
            throw new \Exception('Duplicate sales_details records found: ' . $duplicateCheck->toJson());
        }

        // Check for multiplication in joins
        $joinCheck = DB::table('crm_sales as s')
            ->select('s.id', DB::raw('COUNT(*) as join_multiplier'))
            ->join('crm_sales_details as sd', 's.id', '=', 'sd.sale_id')
            ->join('crm_mapping_sale as ms', 's.id', '=', 'ms.sale_id')
            ->join('crm_mappings as m', 'ms.mapping_id', '=', 'm.id')
            ->whereIn('s.product_id', $productIds)
            ->whereIn('sd.div_id', $divisionIds)
            ->groupBy('s.id')
            ->having('join_multiplier', '>', 1)
            ->get();

        if ($joinCheck->isNotEmpty()) {
            throw new \Exception('Join multiplication detected: ' . $joinCheck->toJson());
        }

        return 'No quantity issues detected';
    }

    private function getAvgProductPriceQuery(): string
    {
        return 'COALESCE(
            (
                SELECT avg_price
                FROM crm_product_prices
                WHERE crm_product_prices.product_id = crm_core_sales.product_id
                AND crm_product_prices.deleted_at IS NULL
                AND crm_product_prices.distributor_id = crm_core_sales.distributor_id
                AND crm_product_prices.from_date <= crm_core_sales.date
                AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_core_sales.date)
                LIMIT 1
            ),
            (
                SELECT avg_price
                FROM crm_product_prices
                WHERE crm_product_prices.product_id = crm_core_sales.product_id
                AND crm_product_prices.deleted_at IS NULL
                AND crm_product_prices.distributor_id IS NULL
                AND crm_product_prices.from_date <= crm_core_sales.date
                AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_core_sales.date)
                LIMIT 1
            )
        )';
    }


}
