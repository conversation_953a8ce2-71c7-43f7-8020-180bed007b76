<?php

use App\Helpers\Result;
use App\Module;
use App\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

if (!function_exists('resetPermissionModule')) {
    function resetPermissionModule($name)
    {
        $oldModule = Module::where("module", $name)->first();
        $oldModule?->forms->each(fn($item) => $item->forceDelete());
        $oldModule?->permissions->each(fn($item) => $item->forceDelete());
        $oldModule?->forceDelete();
    }
}

if (!function_exists("getUserLocation")) {
    function getUserLocation(?User $user = null): array
    {
        /**@var User */
        $user = $user ?? auth()->user();
        $latitude = request()->header('ll');
        $longitude = request()->header('lg');

        if ($latitude == null || $longitude == null) {
            $preLocation = getLastUserCoordinates($user);
            $latitude = $preLocation?->ll ?? 10;
            $longitude = $preLocation?->lg ?? 10;
        }
        return ['lg' => $longitude, 'll' => $latitude];
    }
}

if (!function_exists('getLastUserCoordinates')) {
    function getLastUserCoordinates(User $user)
    {
        $cacheKey = "user_{$user->id}_last_coordinates";

        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($user) {
            return $user->logActivities()->latest()
                ->whereNotNull("ll")
                ->whereNotNull("lg")
                ->select("ll", "lg")
                ->first();
        });
    }
}


if (!function_exists("isNullable")) {
    function isNullable($value): bool
    {
        return is_null($value) || $value == "null" || $value == "NULL" || $value == NULL || $value == null;
    }
}

if (!function_exists("foreignExists")) {
    function foreignExists(string $table, string $key, $optional = false): bool
    {
        $keyName = $optional ? "Key_name" : "Column_name";
        $query = <<<CRM
        SHOW KEYS FROM crm_$table where  $keyName="$key"
        CRM;
        return count(
                DB::select(
                    DB::raw(
                        $query
                    )->getValue(grammar: DB::getQueryGrammar())
                )
            ) == 1;
    }
}

if (!function_exists("indexExists")) {
    function indexExists(string $table, string $key): bool
    {
        $query = <<<CRM
        SHOW INDEX FROM crm_$table where Key_name="$key"
        CRM;
        return count(
                DB::select(
                    DB::raw(
                        $query
                    )->getValue(grammar: DB::getQueryGrammar())
                )
            ) > 0;
    }
}

if (!function_exists("dropConstraintIfExists")) {
    function dropConstraintIfExists(string $table, string $key)
    {
        if (foreignExists($table, $key, true)) {
            echo " foreign constraint exists ";
            $query = <<<CRM
             ALTER TABLE crm_$table Drop Constraint $key
            CRM;
            DB::select($query);
        }
    }
}
if (!function_exists("dropIndexExists")) {
    function dropIndexIfExists(string $table, string $key)
    {
        if (indexExists($table, $key)) {
            echo " index exists ";
            $query = <<<CRM
             ALTER TABLE crm_$table Drop index $key
            CRM;
            DB::select($query);
        }
    }
}

if (!function_exists("dropConstraintIndexExists")) {
    function dropConstraintIndexExists(string $table, string $key)
    {
        dropConstraintIfExists($table, $key);
        dropIndexIfExists($table, $key);
    }
}


if (!function_exists("fromYearAndMonth")) {
    function fromYearAndMonth($year, $month)
    {
        return Carbon::parse($year . '-' . $month)->startOfMonth();
    }
}

if (!function_exists("toYearAndMonth")) {
    function toYearAndMonth($year, $month)
    {
        return Carbon::parse($year . '-' . $month)->endOfMonth();
    }
}

if (!function_exists('sendNotifictionFCM')) {
    function sendNotifictionFCM($notification_id, $title, $message, $id, $type)
    {

        $accesstoken = 'AAAA_cERdEw:APA91bF5QZnucoIpbXVZU95g3l8LZk079RqIWnjtGiosOd5FbrMMHCtTF1tmprKmRJNyNNTJjEByH41vgYD81RAxLJiM58BKmO4Q1JxiYfcYef138Py5oc-ACJexn1M-EgaMaeTXcO_F';

        $URL = 'https://fcm.googleapis.com/v1/projects/gemstone-4cf03/messages:send';


        $post_data = '{
                "to" : "' . $notification_id . '",
                "data" : {
                  "body" : "",
                  "title" : "' . $title . '",
                  "type" : "' . $type . '",
                  "id" : "' . $id . '",
                  "message" : "' . $message . '",
                },
                "notification" : {
                     "body" : "' . $message . '",
                     "title" : "' . $title . '",
                      "type" : "' . $type . '",
                     "id" : "' . $id . '",
                     "message" : "' . $message . '",
                    "icon" : "new",
                    "sound" : "default"
                    },

              }';
        // print_r($post_data);die;

        $crl = curl_init();

        $headr = array();
        $headr[] = 'Content-type: application/json';
        $headr[] = 'Authorization: Bearer=' . $accesstoken;
        curl_setopt($crl, CURLOPT_SSL_VERIFYPEER, false);

        curl_setopt($crl, CURLOPT_URL, $URL);
        curl_setopt($crl, CURLOPT_HTTPHEADER, $headr);

        curl_setopt($crl, CURLOPT_POST, true);
        curl_setopt($crl, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($crl, CURLOPT_RETURNTRANSFER, true);

        $rest = curl_exec($crl);

        if ($rest === false) {
            // throw new Exception('Curl error: ' . curl_error($crl));
            //print_r('Curl error: ' . curl_error($crl));
            $result_noti = 0;
        } else {

            $result_noti = 1;
        }

        //curl_close($crl);
        //print_r($result_noti);die;
        return $result_noti;
    }


}
if (!function_exists('generateIteration')) {
    function generateIteration($input): Generator
    {
        for ($i = 1; $i <= $input; $i++) {
            yield $i;
        }
    }
}

if (!function_exists('loop')) {
    function loop(int $serviceNumber, int $queuesNumber = 1, int $proccessesPerServiceNumber = 4): array
    {
        $data = [];
        $services = generateIteration($serviceNumber);

        foreach ($services as $key) {
            $queueIterator = generateIteration($queuesNumber);
            $queues = [];
            foreach ($queueIterator as $number) {
                $queues[] = 'service_' . $key . '_queue_' . $number;
            }
            $data[] = [
                'supervisor-' . $key => [
                    'connection' => 'redis',
                    'queue' => $queues,
                    'balance' => 'auto',
                    'processes' => $proccessesPerServiceNumber,
                    'tries' => 1,
                ]
            ];
        }

        return $data;
    }
}

if(!function_exists('replacePlaceholders')){
    function replacePlaceholders(string $template, array $keyValues): string {
        foreach ($keyValues as $key => $value) {
            $template = str_replace(":$key", $value, $template);
        }
        return $template;
    }
}


if (!function_exists('tryCatch')) {
    /**
     * Executes a callable that returns a promise and returns a standardized result object
     * containing either the resolved value or the error.
     * This function never throws an error, instead returning the error as part of the result object.
     *
     * @param callable $fn The function to execute
     * @return Result A standardized result object
     */
    function tryCatch(callable $fn): Result {
        try {
            $value = $fn();
            return Result::success($value);
        } catch (Throwable $error) {
            return Result::failure($error);
        }
    }
}


if(!function_exists('getDecimalPlaces')){
    function getDecimalPlaces($number): int
    {
        $parts = explode(".", (string) $number);
        return isset($parts[1]) ? strlen(rtrim($parts[1], "0")) : 0;
    }
}
