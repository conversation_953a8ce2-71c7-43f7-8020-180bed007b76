<?php

namespace App\Console\Commands\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleRepository;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesDistributionService;
use App\Services\Enums\SaleDistribution;
use App\Services\Enums\Ceiling;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RetryDistributionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:retry-distribution
                            {sale_id : The sale ID to retry distribution for}
                            {--reset-status : Reset the sale ceiling status before retrying}
                            {--dry-run : Show what would happen without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry distribution for a specific sale that failed to create sale details';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $saleId = $this->argument('sale_id');
        $resetStatus = $this->option('reset-status');
        $dryRun = $this->option('dry-run');

        $this->info("Retrying distribution for Sale ID: {$saleId}");
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No changes will be made");
        }
        $this->newLine();

        // Get the sale
        $sale = Sale::with(['details', 'mappings'])->find($saleId);
        if (!$sale) {
            $this->error("Sale ID {$saleId} not found");
            return 1;
        }

        $this->displaySaleStatus($sale);
        $this->newLine();

        // Check if sale already has details
        if ($sale->details->count() > 0) {
            $this->warn("Sale already has {$sale->details->count()} detail records. Distribution may have already succeeded.");
            if (!$this->confirm('Do you want to continue anyway?')) {
                return 0;
            }
        }

        // Reset status if requested
        if ($resetStatus && !$dryRun) {
            $this->info("Resetting sale ceiling status...");
            $sale->ceiling = Ceiling::BELOW->value; // Reset to BELOW
            $sale->save();

            // Remove existing mapping if any
            DB::table('mapping_sale')->where('sale_id', $sale->id)->delete();
            $this->info("Sale status reset and mappings removed.");
        }

        // Attempt distribution
        $this->info("Attempting distribution...");

        try {
            if ($dryRun) {
                $this->info("DRY RUN: Would attempt distribution with STORES strategy");
                $this->info("Expected: Create sale details and attach mapping");
                return 0;
            }

            $result = $this->attemptDistribution($sale);

            if ($result) {
                $this->info("✅ Distribution successful!");

                // Reload and verify
                $sale->refresh();
                $detailsCount = $sale->details()->count();
                $totalQuantity = $sale->details()->sum('quantity');

                $this->info("Verification:");
                $this->line("- Details created: {$detailsCount}");
                $this->line("- Total quantity: {$totalQuantity}");
                $this->line("- Quantity match: " . (abs($totalQuantity - $sale->quantity) < 0.01 ? 'Yes' : 'No'));

            } else {
                $this->error("❌ Distribution failed!");
                $this->info("Check the logs for detailed error information.");
            }

        } catch (\Exception $e) {
            $this->error("Exception during distribution: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    private function displaySaleStatus(Sale $sale): void
    {
        $this->info('=== Current Sale Status ===');
        $this->table(
            ['Property', 'Value'],
            [
                ['ID', $sale->id],
                ['Date', $sale->date],
                ['Product ID', $sale->product_id],
                ['Distributor ID', $sale->distributor_id],
                ['Quantity', $sale->quantity],
                ['Ceiling Status', $sale->ceiling],
                ['Details Count', $sale->details->count()],
                ['Mappings Count', $sale->mappings->count()],
            ]
        );
    }

    private function attemptDistribution(Sale $sale): bool
    {
        // Create the distribution components
        $salesService = SalesDistributionService::make(SaleDistribution::NORMAL, DistributionType::STORES);
        $saleDetailFactory = new SaleDetailFactory();
        $algorithm = new SplitDistributionAlgorithm($salesService, $saleDetailFactory);
        $saleRepository = new SaleRepository();
        $saleCreator = new SaleCreator($saleRepository);

        // Create a mock ceiling sale for the strategy
        $ceilingSale = new \stdClass();
        $ceilingSale->id = 'mock_ceiling_sale';
        $ceilingSale->mapping_id = 123905; // From the investigation

        $this->info("Distribution configuration:");
        $this->line("- Algorithm: SplitDistributionAlgorithm");
        $this->line("- Distribution Type: STORES");
        $this->line("- Mapping ID: {$ceilingSale->mapping_id}");
        $this->newLine();

        // Attempt the distribution directly
        $salesContributionBaseOn = [1, 2, 3, 4, 5]; // From the logs

        Log::info('RetryDistributionCommand: Starting distribution retry', [
            'sale_id' => $sale->id,
            'command' => 'distribution:retry-distribution'
        ]);

        $result = $algorithm->distributeExcessSale($sale, $salesContributionBaseOn, null);

        if ($result) {
            // Attach mapping if distribution succeeded
            $saleCreator->attachMapping($sale, $ceilingSale->mapping_id);
        }

        Log::info('RetryDistributionCommand: Distribution retry completed', [
            'sale_id' => $sale->id,
            'success' => $result
        ]);

        return $result;
    }
}
