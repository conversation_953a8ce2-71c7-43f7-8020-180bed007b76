<?php

namespace App\Console\Commands\Distribution;

use App\Sale;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\SalesDistributionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InvestigateDistributionFailureCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:investigate-failure
                            {sale_id : The sale ID to investigate}
                            {--show-query : Show the actual SQL query being executed}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Investigate why distribution failed for a specific sale by analyzing database state and query conditions';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $saleId = $this->argument('sale_id');
        $showQuery = $this->option('show-query');

        $this->info("Investigating distribution failure for Sale ID: {$saleId}");
        $this->newLine();

        // Get the sale details
        $sale = Sale::with(['details', 'mappings'])->find($saleId);
        if (!$sale) {
            $this->error("Sale ID {$saleId} not found");
            return 1;
        }

        $this->displaySaleInfo($sale);
        $this->newLine();

        // Check if sale has details
        $this->checkSaleDetails($sale);
        $this->newLine();

        // Investigate the distribution query conditions
        $this->investigateDistributionQuery($sale, $showQuery);
        $this->newLine();

        // Check mapping and distribution type configuration
        $this->checkMappingConfiguration($sale);
        $this->newLine();

        // Check product ceilings
        $this->checkProductCeilings($sale);

        return 0;
    }

    private function displaySaleInfo(Sale $sale): void
    {
        $this->info('=== Sale Information ===');
        $this->table(
            ['Property', 'Value'],
            [
                ['ID', $sale->id],
                ['Date', $sale->date],
                ['Product ID', $sale->product_id],
                ['Distributor ID', $sale->distributor_id],
                ['Quantity', $sale->quantity],
                ['Value', $sale->value],
                ['Bonus', $sale->bonus],
                ['Ceiling Status', $sale->ceiling],
                ['Created At', $sale->created_at],
            ]
        );
    }

    private function checkSaleDetails(Sale $sale): void
    {
        $this->info('=== Sale Details Check ===');

        $detailsCount = $sale->details->count();
        $totalQuantity = $sale->details->sum('quantity');
        $totalValue = $sale->details->sum('value');

        $this->table(
            ['Metric', 'Value', 'Status'],
            [
                ['Details Count', $detailsCount, $detailsCount > 0 ? '✓' : '✗'],
                ['Total Detail Quantity', $totalQuantity, ''],
                ['Total Detail Value', $totalValue, ''],
                ['Quantity Match', '', abs($totalQuantity - $sale->quantity) < 0.01 ? '✓' : '✗'],
                ['Value Match', '', abs($totalValue - $sale->value) < 0.01 ? '✓' : '✗'],
            ]
        );

        if ($detailsCount > 0) {
            $this->info('Sale Details:');
            $this->table(
                ['ID', 'Div ID', 'Line ID', 'Brick ID', 'Quantity', 'Value', 'Ratio'],
                $sale->details->map(fn($detail) => [
                    $detail->id,
                    $detail->div_id,
                    $detail->line_id,
                    $detail->brick_id,
                    $detail->quantity,
                    $detail->value,
                    $detail->ratio ?? 'N/A'
                ])->toArray()
            );
        }
    }

    private function investigateDistributionQuery(Sale $sale, bool $showQuery): void
    {
        $this->info('=== Distribution Query Investigation ===');

        // Test the exact conditions from the log
        $date = $sale->date;
        $productId = $sale->product_id;
        $distributorIds = [1, 2, 3, 4, 5]; // From the log
        $divisionIds = [608,609,610,611,622,635,637,638,643,647,648,655,656,662]; // From the log

        $this->info("Testing distribution query with:");
        $this->line("- Date: {$date}");
        $this->line("- Product ID: {$productId}");
        $this->line("- Distributor IDs: " . implode(', ', $distributorIds));
        $this->line("- Division IDs: " . implode(', ', $divisionIds));
        $this->newLine();

        // Test primary distribution (no division filter)
        $this->testDistributionQuery($date, $productId, $distributorIds, [], $showQuery, 'Primary (90%)');

        // Test secondary distribution (with division filter)
        $this->testDistributionQuery($date, $productId, $distributorIds, $divisionIds, $showQuery, 'Secondary (10%)');
    }

    private function testDistributionQuery(string $date, int $productId, array $distributorIds, array $divisionIds, bool $showQuery, string $type): void
    {
        $this->info("--- {$type} Distribution Query ---");

        // Create SalesService instance with distribution type 16 (STORES from log)
        $salesService = SalesDistributionService::make(SaleDistribution::NORMAL, DistributionType::STORES);

        $this->info("SalesService configuration:");
        $this->line("- Sale Distribution: NORMAL");
        $this->line("- Distribution Type: STORES (value: " . DistributionType::STORES->value . ")");
        $this->line("- Expected behavior: LEFT JOIN with product_ceilings, no quantity constraints");

        if ($showQuery) {
            DB::enableQueryLog();
        }

        try {
            $ratios = $salesService->getRatiosForDistribution($date, $productId, $distributorIds, $divisionIds);

            $this->table(
                ['Metric', 'Value'],
                [
                    ['Ratios Count', $ratios->count()],
                    ['Total Percentage', $ratios->sum('percentage')],
                    ['Has Results', $ratios->count() > 0 ? 'Yes' : 'No']
                ]
            );

            if ($ratios->count() > 0) {
                $this->info('Distribution Ratios:');
                $this->table(
                    ['Div ID', 'Line ID', 'Brick ID', 'Percentage'],
                    $ratios->map(fn($ratio) => [
                        $ratio->div_id,
                        $ratio->line_id,
                        $ratio->brick_id,
                        $ratio->percentage
                    ])->toArray()
                );
            } else {
                $this->warn('No distribution ratios found!');
            }

            if ($showQuery) {
                $queries = DB::getQueryLog();
                if (!empty($queries)) {
                    $lastQuery = end($queries);
                    $this->info('SQL Query:');
                    $this->line($lastQuery['query']);
                    $this->info('Bindings:');
                    $this->line(json_encode($lastQuery['bindings']));
                }
                DB::disableQueryLog();
            }

        } catch (\Exception $e) {
            $this->error("Query failed: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function checkMappingConfiguration(Sale $sale): void
    {
        $this->info('=== Mapping Configuration Check ===');

        // Check mapping_sale records
        $mappingSales = DB::table('mapping_sale')
            ->where('sale_id', $sale->id)
            ->get();

        $this->info("Mapping Sale Records: " . $mappingSales->count());

        if ($mappingSales->count() > 0) {
            foreach ($mappingSales as $mappingSale) {
                $mapping = DB::table('mappings')
                    ->where('id', $mappingSale->mapping_id)
                    ->first();

                if ($mapping) {
                    $this->table(
                        ['Property', 'Value'],
                        [
                            ['Mapping ID', $mapping->id],
                            ['Code', $mapping->code],
                            ['Name', $mapping->name],
                            ['Distributor ID', $mapping->distributor_id],
                            ['Line ID', $mapping->line_id],
                            ['Unified Pharmacy Type ID', $mapping->unified_pharmacy_type_id ?? 'NULL'],
                            ['Exception', $mapping->exception ? 'Yes' : 'No'],
                            ['From Date', $mapping->from_date],
                            ['To Date', $mapping->to_date ?? 'NULL'],
                        ]
                    );
                }
            }
        } else {
            $this->warn('No mapping_sale records found for this sale!');
        }
    }

    private function checkProductCeilings(Sale $sale): void
    {
        $this->info('=== Product Ceilings Check ===');

        $ceilings = DB::table('product_ceilings')
            ->where('product_id', $sale->product_id)
            ->where(function($q) use ($sale) {
                $q->where('from_date', '<=', $sale->date)
                  ->where(function($q2) use ($sale) {
                      $q2->where('to_date', '>', $sale->date)
                         ->orWhereNull('to_date');
                  });
            })
            ->get();

        $this->info("Product Ceiling Records: " . $ceilings->count());

        if ($ceilings->count() > 0) {
            $this->table(
                ['ID', 'Units', 'Type', 'From Date', 'To Date'],
                $ceilings->map(fn($ceiling) => [
                    $ceiling->id,
                    $ceiling->units,
                    $ceiling->type,
                    $ceiling->from_date,
                    $ceiling->to_date ?? 'NULL'
                ])->toArray()
            );
        } else {
            $this->warn('No product ceiling records found for this product and date!');
        }
    }
}
