<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Carbon\Carbon;

class RollbackDistribution extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:rollback
                            {--from-date= : Start date for rollback (YYYY-MM-DD)}
                            {--to-date= : End date for rollback (YYYY-MM-DD)}
                            {--distribution-type= : Distribution type (1=Private Pharmacy, 16=Stores, 19=Local Chains) - processes ALL sales of this type}
                            {--product-ids= : Comma-separated product IDs}
                            {--distributor-ids= : Comma-separated distributor IDs}
                            {--dry-run : Preview changes without executing}
                            {--backup : Create backup before rollback}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Complete rollback of distribution operations by removing distributed sales, normal/limited sales, and restoring original sales ceiling status. Supports batch operations by distribution type.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $this->info('=== DISTRIBUTION ROLLBACK COMMAND ===');

            // Validate options
            if (!$this->validateOptions()) {
                return 1;
            }

            // Analyze what will be rolled back
            $analysis = $this->analyzeDistribution();
            $this->displayAnalysis($analysis);

            $totalSalesToRemove = $analysis['distributed_sales_count'] + $analysis['normal_sales_count'];
        if ($totalSalesToRemove === 0) {
                $this->info('No sales found for rollback with the specified criteria.');
                return 0;
            }

            // Confirm proceed
            if (!$this->confirmProceed($analysis)) {
                $this->info('Rollback cancelled.');
                return 0;
            }

            // Create backup if requested
            if ($this->option('backup') && !$this->option('dry-run')) {
                $this->createBackupTables();
            }

            // Execute rollback
            if (!$this->option('dry-run')) {
                $this->executeRollback($analysis);
                $this->displayRollbackSummary($analysis);
            } else {
                $this->info('🔍 DRY RUN: No changes were made to the database.');
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ ERROR: ' . $e->getMessage());
            Log::error('Distribution rollback command failed', [
                'error' => $e->getMessage(),
                'options' => $this->options()
            ]);
            return 1;
        }
    }

    /**
     * Validate command options
     */
    private function validateOptions(): bool
    {
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');

        if (!$fromDate || !$toDate) {
            $this->error('Both --from-date and --to-date are required');
            return false;
        }

        try {
            $from = Carbon::createFromFormat('Y-m-d', $fromDate);
            $to = Carbon::createFromFormat('Y-m-d', $toDate);

            if ($from->gt($to)) {
                $this->error('from-date cannot be after to-date');
                return false;
            }
        } catch (\Exception $e) {
            $this->error('Invalid date format. Use YYYY-MM-DD');
            return false;
        }

        $distributionType = $this->option('distribution-type');
        if ($distributionType) {
            $validTypes = [
                DistributionType::PRIVATE_PHARMACY->value,
                DistributionType::STORES->value,
                DistributionType::LOCAL_CHAINS->value
            ];

            if (!in_array((int)$distributionType, $validTypes)) {
                $this->error('distribution-type must be one of: ' . implode(', ', $validTypes) . ' (1=Private Pharmacy, 16=Stores, 19=Local Chains)');
                return false;
            }
        }

        return true;
    }

    /**
     * Analyze what will be affected by the rollback
     */
    private function analyzeDistribution(): array
    {
        $distributedSalesQuery = $this->buildDistributedSalesQuery();
        $distributedSales = $distributedSalesQuery->get();

        // Get original sales that will be restored
        $originalSalesCount = 0;
        $allOriginalSaleIds = [];

        if ($distributedSales->isNotEmpty()) {
            foreach ($distributedSales as $distributedSale) {
                if ($distributedSale->sale_ids) {
                    $originalSaleIds = explode(',', $distributedSale->sale_ids);
                    $allOriginalSaleIds = array_merge($allOriginalSaleIds, $originalSaleIds);
                }
            }
            $allOriginalSaleIds = array_unique($allOriginalSaleIds);
            $originalSalesCount = count($allOriginalSaleIds);
        }

        // Get normal/limited sales that also need to be rolled back
        $normalSalesQuery = $this->buildNormalSalesQuery($allOriginalSaleIds);
        $normalSales = $normalSalesQuery->get();

        $analysis = [
            'distributed_sales_count' => $distributedSales->count(),
            'distributed_sales' => $distributedSales,
            'normal_sales_count' => $normalSales->count(),
            'normal_sales' => $normalSales,
            'original_sales_count' => $originalSalesCount,
            'original_sale_ids' => $allOriginalSaleIds,
            'affected_products' => $distributedSales->pluck('product_id')->unique()->count(),
            'affected_distributors' => $distributedSales->pluck('distributor_id')->unique()->count(),
            'total_distributed_value' => $distributedSales->sum('value'),
            'total_normal_value' => $normalSales->sum('value'),
            'date_range' => [
                'from' => $this->option('from-date'),
                'to' => $this->option('to-date')
            ]
        ];

        // Get sales details count for both distributed and normal sales
        $allSaleIds = $distributedSales->pluck('id')->merge($normalSales->pluck('id'));
        if ($allSaleIds->isNotEmpty()) {
            $analysis['sales_details_count'] = SaleDetail::whereIn('sale_id', $allSaleIds)->count();
        } else {
            $analysis['sales_details_count'] = 0;
        }

        return $analysis;
    }

    /**
     * Build query for distributed sales based on options
     */
    private function buildDistributedSalesQuery(): Builder
    {
        $query = Sale::query()->distributed()
            ->whereNotNull('sale_ids')
            ->whereBetween('date', [$this->option('from-date'), $this->option('to-date')]);

        // Filter by distribution type if specified
        if ($distributionType = $this->option('distribution-type')) {
            $distributionTypeEnum = DistributionType::fromValue((int)$distributionType);

            $query->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
                    ->join('mappings', function ($join) use ($distributionTypeEnum) {
                        $join->on('mapping_sale.mapping_id', 'mappings.id');

                        if ($distributionTypeEnum === DistributionType::PRIVATE_PHARMACY) {
                            // PRIVATE_PHARMACY accepts sales with PRIVATE_PHARMACY type, any type except STORES and LOCAL_CHAINS, and null/unset types
                            $join->where(function ($q) {
                                $q->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::STORES->value)
                                    ->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::LOCAL_CHAINS->value)
                                    ->orWhere('mappings.unified_pharmacy_type_id', null);
                            });
                        } else {
                            // For STORES and LOCAL_CHAINS, match exact type
                            $join->where('mappings.unified_pharmacy_type_id', $distributionTypeEnum->value);
                        }
                    })
                    ->select('sales.*'); // Ensure we only select sales columns to avoid conflicts
        }

        if ($productIds = $this->option('product-ids')) {
            $productIds = array_map('intval', explode(',', $productIds));
            $query->whereIn('sales.product_id', $productIds);
        }

        if ($distributorIds = $this->option('distributor-ids')) {
            $distributorIds = array_map('intval', explode(',', $distributorIds));
            $query->whereIn('sales.distributor_id', $distributorIds);
        }

        return $query;
    }

    /**
     * Build query for normal/limited sales that reference original sales
     */
    private function buildNormalSalesQuery(array $originalSaleIds): Builder
    {
        if (empty($originalSaleIds)) {
            return Sale::query()->whereRaw('1 = 0'); // Return empty query
        }

        $query = Sale::query()->normal()
            ->whereNotNull('sale_ids')
            ->whereBetween('date', [$this->option('from-date'), $this->option('to-date')]);

        // Filter by sales that reference the original sales
        $query->where(function ($q) use ($originalSaleIds) {
            foreach ($originalSaleIds as $originalSaleId) {
                $q->orWhereRaw('FIND_IN_SET(?, sale_ids)', [$originalSaleId]);
            }
        });

        // Apply same filters as distributed sales for consistency
        if ($productIds = $this->option('product-ids')) {
            $productIds = array_map('intval', explode(',', $productIds));
            $query->whereIn('sales.product_id', $productIds);
        }

        if ($distributorIds = $this->option('distributor-ids')) {
            $distributorIds = array_map('intval', explode(',', $distributorIds));
            $query->whereIn('sales.distributor_id', $distributorIds);
        }

        return $query;
    }

    /**
     * Display analysis results
     */
    private function displayAnalysis(array $analysis): void
    {
        $this->newLine();
        $this->info('=== ROLLBACK ANALYSIS ===');

        $totalSalesToRemove = $analysis['distributed_sales_count'] + $analysis['normal_sales_count'];
        $totalValueToRemove = $analysis['total_distributed_value'] + $analysis['total_normal_value'];

        $headers = ['Metric', 'Value'];
        $rows = [
            ['Date Range', "{$analysis['date_range']['from']} to {$analysis['date_range']['to']}"],
            ['Distributed Sales to Remove', $analysis['distributed_sales_count']],
            ['Normal/Limited Sales to Remove', $analysis['normal_sales_count']],
            ['Total Sales to Remove', $totalSalesToRemove],
            ['Sales Details to Remove', $analysis['sales_details_count']],
            ['Original Sales to Restore', $analysis['original_sales_count']],
            ['Affected Products', $analysis['affected_products']],
            ['Affected Distributors', $analysis['affected_distributors']],
            ['Total Distributed Value', number_format($analysis['total_distributed_value'], 2)],
            ['Total Normal/Limited Value', number_format($analysis['total_normal_value'], 2)],
            ['Total Value to Remove', number_format($totalValueToRemove, 2)],
        ];

        // Add distribution type filter info if specified
        if ($distributionType = $this->option('distribution-type')) {
            $distributionTypeEnum = DistributionType::fromValue((int)$distributionType);
            $rows[] = ['Distribution Type Filter', $distributionTypeEnum->getName() . " ({$distributionTypeEnum->value})"];
        }

        $this->table($headers, $rows);

        // Display sample distributed sales
        if ($analysis['distributed_sales_count'] > 0) {
            $this->newLine();
            $this->info('=== SAMPLE DISTRIBUTED SALES ===');

            $sampleHeaders = ['ID', 'Product ID', 'Distributor ID', 'Date', 'Quantity', 'Value', 'Ceiling'];
            $sampleRows = [];

            $sample = $analysis['distributed_sales']->take(3);
            foreach ($sample as $sale) {
                $sampleRows[] = [
                    $sale->id,
                    $sale->product_id,
                    $sale->distributor_id ?? 'N/A',
                    $sale->date,
                    $sale->quantity,
                    number_format($sale->value, 2),
                    'DISTRIBUTED'
                ];
            }

            $this->table($sampleHeaders, $sampleRows);

            if ($analysis['distributed_sales_count'] > 3) {
                $this->info("... and " . ($analysis['distributed_sales_count'] - 3) . " more distributed sales");
            }
        }

        // Display sample normal/limited sales
        if ($analysis['normal_sales_count'] > 0) {
            $this->newLine();
            $this->info('=== SAMPLE NORMAL/LIMITED SALES ===');

            $sampleHeaders = ['ID', 'Product ID', 'Distributor ID', 'Date', 'Quantity', 'Value', 'Ceiling'];
            $sampleRows = [];

            $sample = $analysis['normal_sales']->take(3);
            foreach ($sample as $sale) {
                $sampleRows[] = [
                    $sale->id,
                    $sale->product_id,
                    $sale->distributor_id ?? 'N/A',
                    $sale->date,
                    $sale->quantity,
                    number_format($sale->value, 2),
                    'NORMAL'
                ];
            }

            $this->table($sampleHeaders, $sampleRows);

            if ($analysis['normal_sales_count'] > 3) {
                $this->info("... and " . ($analysis['normal_sales_count'] - 3) . " more normal/limited sales");
            }
        }
    }

    /**
     * Confirm with user before proceeding
     */
    private function confirmProceed(array $analysis): bool
    {
        if ($this->option('force') || $this->option('dry-run')) {
            return true;
        }

        $this->newLine();
        $this->warn('=== WARNING ===');

        if ($distributionType = $this->option('distribution-type')) {
            $distributionTypeEnum = DistributionType::fromValue((int)$distributionType);
            $this->warn("This will perform a BATCH ROLLBACK for ALL {$distributionTypeEnum->getName()} sales in the specified date range.");
        }

        $totalSalesToRemove = $analysis['distributed_sales_count'] + $analysis['normal_sales_count'];
        $this->warn("This will permanently delete {$analysis['distributed_sales_count']} distributed sales");
        $this->warn("and {$analysis['normal_sales_count']} normal/limited sales");
        $this->warn("and {$analysis['sales_details_count']} related sales details.");
        $this->warn("Total sales to be removed: {$totalSalesToRemove}");
        $this->warn("It will restore {$analysis['original_sales_count']} original sales by changing their ceiling status from 'ABOVE' to 'BELOW'.");

        if (!$this->option('backup')) {
            $this->newLine();
            $this->comment('NOTE: No backup will be created. Consider using --backup option.');
        }

        return $this->confirm('Do you want to proceed?');
    }

    /**
     * Create backup tables
     */
    private function createBackupTables(): void
    {
        $timestamp = date('Y_m_d_H_i_s');
        $distributionType = $this->option('distribution-type');
        $suffix = $distributionType ? "_type_{$distributionType}_{$timestamp}" : "_{$timestamp}";

        $this->info('Creating backup tables...');

        // Build the same query conditions as the main rollback query
        $distributedSalesQuery = $this->buildDistributedSalesQuery();
        $distributedSaleIds = $distributedSalesQuery->pluck('id');

        // Get original sale IDs for normal sales query
        $originalSaleIds = [];
        foreach ($distributedSalesQuery->get() as $sale) {
            if ($sale->sale_ids) {
                $originalSaleIds = array_merge($originalSaleIds, explode(',', $sale->sale_ids));
            }
        }
        $originalSaleIds = array_unique($originalSaleIds);

        // Get normal/limited sales that will also be deleted
        $normalSalesQuery = $this->buildNormalSalesQuery($originalSaleIds);
        $normalSaleIds = $normalSalesQuery->pluck('id');

        // Combine all sale IDs that will be deleted
        $allSaleIdsToDelete = $distributedSaleIds->merge($normalSaleIds);

        if ($allSaleIdsToDelete->isNotEmpty()) {
            $saleIdsString = $allSaleIdsToDelete->implode(',');

            // Backup all sales that will be deleted (distributed + normal/limited)
            DB::statement("CREATE TABLE sales_backup{$suffix} AS SELECT * FROM crm_sales WHERE id IN ({$saleIdsString})");

            // Backup sales_details (that will be deleted)
            DB::statement("CREATE TABLE sales_details_backup{$suffix} AS SELECT * FROM crm_sales_details WHERE sale_id IN ({$saleIdsString})");

            // Backup original sales (that will be restored)
            if (!empty($originalSaleIds)) {
                $originalSaleIdsString = implode(',', $originalSaleIds);
                DB::statement("CREATE TABLE original_sales_backup{$suffix} AS SELECT * FROM crm_sales WHERE id IN ({$originalSaleIdsString})");
            }

            $this->info("✅ Backup tables created: sales_backup{$suffix}, sales_details_backup{$suffix}, original_sales_backup{$suffix}");
            $this->info("   Backed up {$distributedSaleIds->count()} distributed sales and {$normalSaleIds->count()} normal/limited sales");
        } else {
            $this->info("No sales found to backup.");
        }
    }

    /**
     * Execute the actual rollback
     */
    private function executeRollback(array $analysis): void
    {
        $this->info('Executing rollback...');

        $progressBar = $this->output->createProgressBar(4);
        $progressBar->start();

        DB::transaction(function () use ($analysis, $progressBar) {
            $distributedSaleIds = $analysis['distributed_sales']->pluck('id');
            $normalSaleIds = $analysis['normal_sales']->pluck('id');
            $allSaleIds = $distributedSaleIds->merge($normalSaleIds);

            // Step 1: Delete sales details for all sales (distributed + normal/limited)
            $deletedDetails = SaleDetail::whereIn('sale_id', $allSaleIds)->delete();
            $this->newLine();
            $this->info("✅ Deleted {$deletedDetails} sales details");
            $progressBar->advance();

            // Step 2: Delete distributed sales
            $deletedDistributedSales = Sale::whereIn('id', $distributedSaleIds)->delete();
            $this->info("✅ Deleted {$deletedDistributedSales} distributed sales");
            $progressBar->advance();

            // Step 3: Delete normal/limited sales
            $deletedNormalSales = Sale::whereIn('id', $normalSaleIds)->delete();
            $this->info("✅ Deleted {$deletedNormalSales} normal/limited sales");
            $progressBar->advance();

            // Step 4: Restore original sales using sale_ids from distributed sales
            $updated = $this->restoreOriginalSales($analysis['original_sale_ids']);
            $this->info("✅ Restored {$updated} original sales (ceiling: ABOVE → BELOW)");
            $progressBar->advance();
        });

        $progressBar->finish();
        $this->newLine(2);

        $totalDeletedSales = $analysis['distributed_sales_count'] + $analysis['normal_sales_count'];
        $logData = [
            'deleted_distributed_sales' => $analysis['distributed_sales_count'],
            'deleted_normal_sales' => $analysis['normal_sales_count'],
            'total_deleted_sales' => $totalDeletedSales,
            'deleted_details' => $analysis['sales_details_count'],
            'restored_sales' => $analysis['original_sales_count'],
            'options' => $this->options()
        ];

        if ($distributionType = $this->option('distribution-type')) {
            $distributionTypeEnum = DistributionType::fromValue((int)$distributionType);
            $logData['distribution_type'] = [
                'value' => $distributionTypeEnum->value,
                'name' => $distributionTypeEnum->getName()
            ];
            $logData['operation_type'] = 'batch_rollback_by_distribution_type';
        } else {
            $logData['operation_type'] = 'rollback_by_date_range';
        }

        Log::info('Distribution rollback completed via Artisan command', $logData);
    }

    /**
     * Restore original sales by changing their ceiling status from ABOVE to BELOW
     */
    private function restoreOriginalSales(array $originalSaleIds): int
    {
        if (empty($originalSaleIds)) {
            return 0;
        }

        return Sale::query()->whereIn('id', $originalSaleIds)
            ->original()
            ->update(['ceiling' => Ceiling::BELOW->value]);
    }

    /**
     * Display rollback completion summary
     */
    private function displayRollbackSummary(array $analysis): void
    {
        $this->newLine();
        $this->info('=== ROLLBACK COMPLETED SUCCESSFULLY ===');

        if ($distributionType = $this->option('distribution-type')) {
            $distributionTypeEnum = DistributionType::fromValue((int)$distributionType);
            $this->info("✅ Batch rollback completed for {$distributionTypeEnum->getName()} distribution type");
            $this->info("   Distribution Type: {$distributionTypeEnum->getName()} ({$distributionTypeEnum->value})");
        } else {
            $this->info("✅ Rollback completed for date range");
        }

        $totalSalesRemoved = $analysis['distributed_sales_count'] + $analysis['normal_sales_count'];
        $totalValueRemoved = $analysis['total_distributed_value'] + $analysis['total_normal_value'];

        $this->info("   Date Range: {$analysis['date_range']['from']} to {$analysis['date_range']['to']}");
        $this->info("   Distributed Sales Removed: {$analysis['distributed_sales_count']}");
        $this->info("   Normal/Limited Sales Removed: {$analysis['normal_sales_count']}");
        $this->info("   Total Sales Removed: {$totalSalesRemoved}");
        $this->info("   Sales Details Removed: {$analysis['sales_details_count']}");
        $this->info("   Original Sales Restored: {$analysis['original_sales_count']}");
        $this->info("   Total Value Removed: " . number_format($totalValueRemoved, 2));

        if ($this->option('backup')) {
            $this->info("   Backup tables created for recovery if needed");
        }

        $this->newLine();
        $this->comment('All operations completed successfully. The complete distribution rollback has been applied.');
        $this->comment('Both distributed sales and normal/limited sales created during distribution have been removed.');
    }
}
