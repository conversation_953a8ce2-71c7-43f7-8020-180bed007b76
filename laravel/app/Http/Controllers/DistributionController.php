<?php

namespace App\Http\Controllers;

use App\Services\Sales\Ceiling\SalesCeilingDetectionService;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategyFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * Example controller demonstrating usage of the refactored distribution strategy system
 */
class DistributionController extends Controller
{
    private SalesCeilingDetectionService $distributionService;
    private DistributionStrategyFactory $strategyFactory;

    public function __construct(
        SalesCeilingDetectionService $distributionService,
        DistributionStrategyFactory  $strategyFactory
    ) {
        $this->distributionService = $distributionService;
        $this->strategyFactory = $strategyFactory;
    }

    /**
     * Process distribution for a specific strategy type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function processDistribution(Request $request): JsonResponse
    {
        $request->validate([
            'distribution_type' => 'required|integer|in:1,2,3',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'product_ids' => 'array',
            'distributor_ids' => 'array',
            'positive' => 'boolean'
        ]);

        try {
            $type = DistributionType::from($request->input('distribution_type'));

            // Get ceiling sales data
            $ceilingSales = $this->distributionService->queryCeilingSales(
                $type,
                $request->input('from_date'),
                $request->input('to_date'),
                $request->input('product_ids', []),
                $request->input('distributor_ids', [])
            );

            if ($ceilingSales->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No ceiling sales found for the specified criteria',
                    'data' => []
                ]);
            }

            // Calculate total sales before distribution for integrity check
            $totalSalesBeforeDistribution = $this->calculateTotalSalesValue($ceilingSales);

            // Create appropriate strategy
            $strategy = $this->strategyFactory->create($type);

            // Process distribution
            $result = $strategy->recalculateAndDistributeDifferences($ceilingSales);

            if ($result) {
                // Calculate total sales after distribution for integrity check
                $totalSalesAfterDistribution = $this->calculateTotalSalesValueAfterDistribution($ceilingSales);

                // Check for data integrity
                $integrityCheck = abs($totalSalesBeforeDistribution - $totalSalesAfterDistribution) < 0.01;

                Log::info('Distribution processed successfully', [
                    'distribution_type' => $request->input('distribution_type'),
                    'ceiling_sales_count' => $ceilingSales->count(),
                    'total_before' => $totalSalesBeforeDistribution,
                    'total_after' => $totalSalesAfterDistribution,
                    'integrity_check' => $integrityCheck
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Distribution processed successfully',
                    'data' => [
                        'processed_sales' => $ceilingSales->count(),
                        'distribution_type' => $request->input('distribution_type'),
                        'total_sales_before' => $totalSalesBeforeDistribution,
                        'total_sales_after' => $totalSalesAfterDistribution,
                        'integrity_check_passed' => $integrityCheck
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Distribution processing failed'
                ], 500);
            }

        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid distribution type: ' . $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Distribution processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing distribution'
            ], 500);
        }
    }

    /**
     * Get available distribution types
     *
     * @return JsonResponse
     */
    public function getAvailableTypes(): JsonResponse
    {
        $types = [
            DistributionType::PRIVATE_PHARMACY->value => 'Private Pharmacy',
            DistributionType::STORES->value => 'Stores',
            DistributionType::LOCAL_CHAINS->value => 'Local Chains'
        ];

        $availableTypes = [];
        foreach ($this->strategyFactory->getAvailableTypes() as $type) {
            $availableTypes[] = [
                'id' => $type,
                'name' => $types[$type] ?? 'Unknown'
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $availableTypes
        ]);
    }

    /**
     * Process distribution by strategy name
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function processDistributionByName(Request $request): JsonResponse
    {
        $request->validate([
            'strategy_name' => 'required|string|in:private_pharmacy,store,local_chain',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'product_ids' => 'array',
            'distributor_ids' => 'array',
            'positive' => 'boolean'
        ]);

        try {
            // Get ceiling sales data
            $ceilingSales = $this->distributionService->queryCeilingSales(
                $request->input('from_date'),
                $request->input('to_date'),
                $request->input('product_ids', []),
                $request->input('distributor_ids', [])
            );

            if ($ceilingSales->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No ceiling sales found for the specified criteria',
                    'data' => []
                ]);
            }

            // Create strategy by name
            $strategy = $this->strategyFactory->createByName($request->input('strategy_name'));

            // Process distribution
            $result = $strategy->recalculateAndDistributeDifferences($ceilingSales);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Distribution processed successfully',
                    'data' => [
                        'processed_sales' => $ceilingSales->count(),
                        'strategy_name' => $request->input('strategy_name')
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Distribution processing failed'
                ], 500);
            }

        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid strategy name: ' . $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Distribution processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing distribution'
            ], 500);
        }
    }

    /**
     * Calculate total sales value from ceiling sales collection
     *
     * @param \Illuminate\Support\Collection $ceilingSales
     * @return float
     */
    private function calculateTotalSalesValue($ceilingSales): float
    {
        return $ceilingSales->sum('number_of_values');
    }

    /**
     * Calculate total sales value after distribution processing
     *
     * @param \Illuminate\Support\Collection $ceilingSales
     * @return float
     */
    private function calculateTotalSalesValueAfterDistribution($ceilingSales): float
    {
        $totalValue = 0;

        foreach ($ceilingSales as $ceilingSale) {
            // Sum values from limited sales (BELOW ceiling)
            $limitedSalesValue = \App\Sale::where('sale_ids', $ceilingSale->sale_ids)
                ->where('ceiling', \App\Services\Enums\Ceiling::BELOW)
                ->sum('value');

            // Sum values from excess sales (DISTRIBUTED ceiling) and their details
            $excessSales = \App\Sale::where('sale_ids', $ceilingSale->sale_ids)
                ->where('ceiling', \App\Services\Enums\Ceiling::DISTRIBUTED)
                ->get();

            $excessSalesValue = 0;
            foreach ($excessSales as $excessSale) {
                $excessSalesValue += $excessSale->details()->sum('value');
            }

            $totalValue += $limitedSalesValue + $excessSalesValue;
        }

        return $totalValue;
    }
}
