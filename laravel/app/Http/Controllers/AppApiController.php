<?php

namespace App\Http\Controllers;

use App\Models\EDetailing\Statistic;
use App\Models\OtherSetting;
use App\Account;
use App\AccountLines;
use App\AccountType;
use App\ActualVisit;
use App\ActualVisitManager;
use App\ActualVisitSetting;
use App\AvRequiredInput;
use App\Brick;
use App\Classes;
use App\DivisionType;
use App\Doctor;
use App\Giveaway;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\AccountTypeDistance;
use App\Models\Attachment;
use App\Models\EDetailing\Presentation;
use App\Models\ListType;
use App\Models\Trace;
use App\Models\UnplannedVisitNumber;
use App\OfficeWorkType;
use App\OwActualVisit;
use App\OwPlanVisit;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\PositionDoubleVisitSetting;
use App\Product;
use App\Services\TracingService;
use App\Setting;
use App\Speciality;
use App\User;
use App\VisitFeedbacks;
use App\VisitGiveaway;
use App\VisitProduct;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Company;
use App\LineBricks;
use App\Position;

class AppApiController extends Controller
{
    public function __construct(private readonly TracingService $tracingService) {}

    public function userData()
    {
        /**@var User */
        $user = Auth::user();
        return response()->json([
            'user' => User::where('id', $user->id)->get()
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'fullname' => $user->fullname,
                        'emp_code' => $user->emp_code,
                        'url' => $user->url,
                        'is_manager' => $user->divisions()->orderBy('division_type_id', 'ASC')->first()?->divisionType?->last_level == 0 ? 1 : 0,
                    ];
                })->collapse()
        ]);
    }

    public function masterData()
    {
        $product_brand_level = ActualVisitSetting::where('key', 'actual_visit_level')->value('value');
        $accountTypes = AccountType::where('deleted_at', null)->get()->map(function ($accountType) {
            return [
                'id' => $accountType->id,
                'name' => $accountType->name,
                'shift_id' => $accountType->shift_id,
                'sort' => $accountType->sort,
                'accepted_distance' => AccountTypeDistance::where('type_id', $accountType->id)->first()?->distance ?? 500,
            ];
        });
        $companyName = Company::first()->name;
        /**
         * @var User $user
         */
        $user = auth()->user();
        $lineIds = $user->userLines()->pluck('id');

        $lines = $user->userLines()->map(function ($line) {
            $setting = UnplannedVisitNumber::where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $line->id))->first();
            return [
                'id' => $line->id,
                'name' => $line->name,
                'unplanned_limit' => $setting->number,
                'attachment_required' => $setting->attachment,
                'payment_required' => $setting->payment,
                'timezone' => $line?->timezone ?? 'Africa/Cairo',
            ];
        });

        $division_type = DivisionType::where('last_level', '=', 1)->first()?->id;
        $divisions = collect([]);

        foreach ($lines as $line) {
            $line = Line::find($line['id']);
            $divisions = $divisions->merge($user->userDivisions($line)->where('division_type_id', $division_type));
        }
        $divisions = $divisions->unique('id')->values()->map(function ($division) {
            return [
                'id' => $division->id,
                'name' => $division->name,
                'type_id' => $division->division_type_id,
                'line_id' => $division->line_id,
            ];
        });

        $bricks = Brick::select(["bricks.id", "bricks.name", 'line_bricks.line_id', 'line_bricks.line_division_id'])
            ->leftJoin("line_bricks", "line_bricks.brick_id", "bricks.id")
            ->whereIntegerInRaw("line_id", $lineIds)
            ->whereIntegerInRaw("line_division_id", $divisions->pluck("id")->toArray())
            ->where('line_bricks.deleted_at', '=', null)
            ->where(fn($q) => $q->where('line_bricks.to_date', '>', (string) Carbon::now())
                ->orWhere('line_bricks.to_date', null))
            ->get();

        $products = collect([]);
        if ($companyName == 'Adamco') {
            $lineProducts = Product::whereHas('lineproducts', function ($q) use ($lineIds) {
                $q->whereIntegerInRaw("line_products.line_id", $lineIds)
                    ->where('line_products.deleted_at', '=', null)
                    ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_products.to_date', null));
            })->with('lineproducts')->get()->unique('id')->values();
            foreach ($lines as $line) {
                $lineProducts = collect($lineProducts->map(function ($product) use ($line) {
                    return [
                        'id' => $product['id'],
                        'name' => $product['name'],
                        'line_id' => $line['id'],
                    ];
                }));
                $products = $products->merge($lineProducts);
            }
            $products = $products->unique(function ($item) {
                return $item['id'] . $item['line_id'];
            });
        } else {
            foreach ($lineIds as $lineId) {
                $products = $products->merge(Product::whereHas('lineproducts', function ($q) use ($lineId) {
                    $q->where("line_products.line_id", $lineId)
                        ->where('line_products.deleted_at', '=', null)
                        ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                            ->orWhere('line_products.to_date', null));
                })->with('lineproducts', fn($q) => $q->where('line_id', $lineId))->get());
            }
            $products = $products->map(function ($product) use ($product_brand_level, $lineIds) {
                return [
                    'id' => $product->id,
                    'name' => $this->getProductName($product, $product_brand_level),
                    'line_id' => $product->lineproducts->first()->line_id,
                ];
            })->unique(function ($item) {
                return $item['name'] . $item['line_id'];
            })->filter(fn($product) => $product['name'] != null)->values();
        }
        $spcialities = Speciality::select(["specialities.id", "specialities.name", "line_specialities.line_id"])
            ->leftJoin("line_specialities", "line_specialities.speciality_id", "specialities.id")
            ->where('line_specialities.deleted_at', '=', null)
            ->where(fn($q) => $q->where('line_specialities.to_date', '>', (string) Carbon::now())
                ->orWhere('line_specialities.to_date', null))
            ->whereIntegerInRaw("line_id", $lineIds)
            ->get();

        $classes = Classes::select(["classes.id", "classes.name", "line_classes.line_id"])
            ->leftJoin("line_classes", "line_classes.class_id", "classes.id")
            ->where('line_classes.deleted_at', '=', null)
            ->where(fn($q) => $q->where('line_classes.to_date', '>', (string) Carbon::now())
                ->orWhere('line_classes.to_date', null))
            ->whereIntegerInRaw("line_id", $lineIds)
            ->get();

        $giveways = Giveaway::select(["id", "name", "line_id"])->whereIntegerInRaw("line_id", $lineIds)
            ->get();

        $visitFeedBack = VisitFeedbacks::select(["id", "notes as name"])->get();
        $managers = collect([]);
        $positions = collect([]);
        foreach ($lineIds as $key => $value) {
            $managers = $managers->merge($user->allAboveUsers(Line::find($value)));
            $positions = $positions->merge(PositionDoubleVisitSetting::users($value));
        }
        $managers = $managers->merge($positions);
        $managers = $managers->unique('id')->values()->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->fullname,
            ];
        });

        $officeWorkTypes = OfficeWorkType::select(["id", "name"])->get();

        $id = 0;
        $settings = ActualVisitSetting::select('id', 'key', 'value')->get()->map(function ($setting) use (&$id) {
            if ($setting->key == 'actual_extra_time') {
                return [
                    'id' => $id += 1,
                    'attribute_name' => $setting->key,
                    'attribute_value' => 3,
                    'domain' => 'Actual'
                ];
            } else {
                return [
                    'id' => $id += 1,
                    'attribute_name' => $setting->key,
                    'attribute_value' => $setting->value ? ($setting->value == "Yes" ? 1 : 0) : "",
                    'domain' => 'Actual'
                ];
            }
        })->filter(fn($item) => $item['attribute_name'] != 'actual_direct')->values();
        $settings = collect($settings)->merge([
            [
                'id' => $id += 1,
                'attribute_name' => 'no_of_products',
                'attribute_value' => 1,
                'domain' => 'Actual'
            ],
            [
                'id' => $id += 1,
                'attribute_name' => 'is_required_product_feedback',
                'attribute_value' => 1,
                'domain' => 'Actual'
            ],
            [
                'id' => $id += 1,
                'attribute_name' => 'is_required_product_comment',
                'attribute_value' => AvRequiredInput::where('name', 'product_comment')->value('select'),
                'domain' => 'Actual'
            ],

        ])->merge(AvRequiredInput::select('name', 'select')->get()->map(function ($setting) use (&$id) {
            return [
                'id' => $id += 1,
                'attribute_name' => $setting->name,
                'attribute_value' => $setting->select,
                'domain' => 'Actual'
            ];
        }))->merge(Setting::select('name', 'value')->get()->map(function ($setting) use (&$id) {
            return [
                'id' => $id += 1,
                'attribute_name' => $setting->name,
                'attribute_value' => $setting->value,
                'domain' => 'Actual'
            ];
        }))
            ->merge(
                collect([
                    [
                        'id' => $id += 1,
                        'attribute_name' => 'timezone',
                        'attribute_value' => $lines[0]['timezone'] ?? 'Africa/Cairo',
                        'domain' => 'User'
                    ]
                ])
            )->merge(
                collect([
                    [
                        'id' => $id += 1,
                        'attribute_name' => 'actual_direct',
                        'attribute_value' => $lines[0]['unplanned_limit'] ?? 3,
                        'domain' => 'Actual'
                    ]
                ])
            );
        // ->merge(
        //     collect([
        //         [
        //             'id' => $id += 1,
        //             'attribute_name' => 'season',
        //             'attribute_value' => 0,
        //             'domain' => 'Actual'
        //         ]
        //     ])
        // );

        $data = collect([
            'lines' => $lines,
            'divisions' => $divisions,
            'bricks' => $bricks,
            'accountTypes' => $accountTypes,
            'products' => $products,
            'spcialities' => $spcialities,
            'classes' => $classes,
            'visitFeedBack' => $visitFeedBack,
            'managers' => $managers,
            'officeWorkTypes' => $officeWorkTypes,
            'giveways' => $giveways,
            'settings' => $settings,
        ]);
        return response()->json([
            "data" => $data,
        ]);
    }
    public function getProductName($product, $level)
    {
        if ($level == 'Product')
            return $product->name;
        elseif ($level == 'Brief')
            return $product->short_name;
        else
            return count($product->brands) > 0 ? $product->brands->first()?->name : $product->name;
    }
    public function getAcountAndDoctors(): JsonResponse
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $lineIds = [];
        $division_type = DivisionType::where('last_level', '=', 1)->first()?->id;
        $divisions = collect([]);

        $lineIds = $lines->pluck('id')->toArray();
        foreach ($lineIds as $line_id) {
            $line = Line::find($line_id);
            $divisions = $divisions->merge($user->userDivisions($line)->where('division_type_id', $division_type));
        }
        $divisions = $divisions->pluck('id')->unique()->toArray();
        $setting = ListType::first()->type == 'Default List' ? true : false;
        $accounts = Account::select([
            'accounts.id',
            'accounts.name',
            'account_lines.line_id',
            'account_lines.line_division_id as div_id',
            DB::raw('IFNULL(crm_account_lines.brick_id,-1) as brick_id'),
            'account_lines.class_id as class_id',
            'accounts.code',
            'accounts.type_id',
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
            DB::raw('IFNULL(crm_accounts.mobile,"") as mobile'),
            DB::raw('IFNULL(crm_accounts.email,"") as email'),
            DB::raw('IFNULL(crm_account_lines.ll,"") as ll'),
            DB::raw('IFNULL(crm_account_lines.lg,"") as lg'),

        ])
            ->join('account_lines', function ($join) use ($lineIds, $divisions) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->whereIntegerInRaw("account_lines.line_id", $lineIds)
                    ->whereIntegerInRaw("account_lines.line_division_id",  $divisions)
                    ->where('account_lines.from_date', '<=', Carbon::now())
                    ->whereNull('account_lines.deleted_at')
                    ->where(fn($q) => $q->where('account_lines.to_date', '>', (string) Carbon::now())
                        ->orWhere('account_lines.to_date', null));
            });
        if (!$setting) {
            $accounts = $accounts
                ->join(
                    'new_account_doctors',
                    function ($join) use ($lineIds) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id')
                            ->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->whereIntegerInRaw("new_account_doctors.line_id", $lineIds)
                            ->whereNull('new_account_doctors.deleted_at')
                            ->where('new_account_doctors.from_date', '<=', Carbon::now())
                            ->where(fn($q) => $q->where('new_account_doctors.to_date', '>', (string) Carbon::now())
                                ->orWhere('new_account_doctors.to_date', null));
                    }
                );
        } else {
            $accounts = $accounts->join('new_account_doctors', function ($join) use ($lineIds) {
                $join->on('accounts.id', 'new_account_doctors.account_id')
                    ->whereIntegerInRaw("new_account_doctors.line_id", $lineIds)
                    ->whereNull('new_account_doctors.deleted_at')
                    ->where('new_account_doctors.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('new_account_doctors.to_date', '>', (string) Carbon::now())
                        ->orWhere('new_account_doctors.to_date', null));;
            });
        }
        $accounts = $accounts->join('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->get()->unique(function ($item) {
                return $item['id'] . $item['div_id'];
            })->values();

        $doctors = Doctor::select([
            'doctors.id',
            'doctors.name',
            'new_account_doctors.line_id',
            'new_account_doctors.account_id',
            'accounts.type_id',
            'doctors.active_date',
            'doctors.inactive_date',
            'doctors.speciality_id',
            'doctors.class_id',
            'doctors.email',
            'doctors.tel',
            'doctors.mobile',
            'doctors.gender',
        ])
            ->leftJoin("new_account_doctors", "new_account_doctors.doctor_id", "doctors.id")
            ->leftJoin("accounts", "new_account_doctors.account_id", "accounts.id")
            ->leftJoin("account_lines", "new_account_doctors.account_id", "account_lines.account_id")
            ->where('new_account_doctors.deleted_at', '=', null)
            ->where('account_lines.deleted_at', '=', null)
            ->where('new_account_doctors.from_date', '<=', Carbon::now())
            ->where('account_lines.from_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '>=', (string) Carbon::now())
                ->orWhere('new_account_doctors.to_date', null))
            ->where(fn($q) => $q->where('account_lines.to_date', '>=', (string) Carbon::now())
                ->orWhere('account_lines.to_date', null))
            ->where(fn($q) => $q->where('accounts.inactive_date', '>=', (string) Carbon::now())
                ->orWhere('accounts.inactive_date', null))
            ->whereIntegerInRaw("new_account_doctors.line_id", $lineIds)
            ->whereIntegerInRaw("account_lines.line_id", $lineIds)
            ->whereIntegerInRaw("account_lines.line_division_id",  $divisions)
            ->get()->unique('id')->values();

        return response()->json([
            "status" => 200,
            "data" => ["accoutns" => $accounts, "doctors" => $doctors]
        ], 200);
    }

    public function userPlans()
    {
        /**@var User $user */
        $user = Auth::user();
        $plans = DB::table('planned_visits')->select(
            'planned_visits.id',
            'lines.id as line_id',
            'line_divisions.id as division_id',
            DB::raw('IFNULL(crm_bricks.id,-1) as brick_id'),
            'planned_visits.account_id as account_id',
            'accounts.name as account',
            'accounts.type_id as type_id',
            'account_types.name as account_type',
            'planned_visits.account_dr_id as doctor_id',
            'doctors.name as doctor',
            'doctors.speciality_id as speciality_id',
            DB::raw('IFNULL(crm_d.id,"") as doc_class'),
            DB::raw('IFNULL(group_concat(distinct crm_account_lines.ll),"") as ll'),
            DB::raw('IFNULL(group_concat(distinct crm_account_lines.lg),"") as lg'),
            // DB::raw('CAST(IFNULL(crm_planned_visits.shift_id,3) AS UNSIGNED) as shift_id'),
            DB::raw('IFNULL(crm_account_types.shift_id,3) as shift_id'),
            DB::raw('IFNULL(group_concat(distinct crm_a.id),"") as acc_class'),
            'visit_types.name as type',
            'visit_types.id as visit_type_id',
            DB::raw('DATE_FORMAT(crm_planned_visits.visit_date, "%Y-%m-%d") as date'),
            DB::raw('TIME(crm_planned_visits.visit_date) as time'),

        )

            ->leftJoin('lines', 'planned_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'planned_visits.div_id', 'line_divisions.id')
            ->leftJoin('accounts', 'planned_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', function ($join) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->on('lines.id', 'account_lines.line_id')
                    ->on('line_divisions.id', 'account_lines.line_division_id')
                    ->where('account_lines.from_date', '<=', now())
                    ->where(fn($q) => $q->where('account_lines.to_date', '>', (string) Carbon::now())
                        ->orWhere('account_lines.to_date', null));
            })
            ->leftJoin('doctors', 'planned_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('visit_types', 'planned_visits.visit_type', 'visit_types.id')
            ->leftJoin(
                'actual_visits',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'actual_visits.plan_id');
                    $join->on('planned_visits.user_id', '=', 'actual_visits.user_id');
                }
            )
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\PlanVisit');
                }
            )
            ->where('planned_visits.user_id', '=', $user->id)
            ->whereDate('planned_visits.visit_date', '>=', Carbon::yesterday())
            ->where('plan_visit_details.approval', 1)
            ->whereNull('actual_visits.plan_id')
            ->orderBy('id', 'ASC')
            ->groupBy('id', "plan_visit_details.approval", "bricks.id")
            ->get()->unique('id')->values()->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'line_id' => $plan->line_id,
                    'division_id' => $plan->division_id,
                    'brick_id' => $plan->brick_id,
                    'account_id' => $plan->account_id,
                    'account' => $plan->account,
                    'type_id' => $plan->type_id,
                    'account_type' => $plan->account_type,
                    'doctor_id' => $plan->doctor_id,
                    'doctor' => $plan->doctor,
                    'doc_class' => $plan->doc_class,
                    'speciality_id' => $plan->speciality_id,
                    'll' => $plan->ll,
                    'lg' => $plan->lg,
                    'shift_id' => $plan->shift_id,
                    'acc_class' => explode(',', $plan->acc_class)[0],
                    'type' => $plan->type,
                    'visit_type_id' => $plan->visit_type_id,
                    'date' => $plan->date,
                    'time' => $plan->time,
                ];
            });
        return response()->json([
            "status" => 200,
            "data" => $plans
        ], 200);
    }


    public function planOw()
    {
        /**@var User $user */
        $user = Auth::user();
        $planned_ow = OwPlanVisit::select(
            'ow_plan_visits.id',
            'ow_plan_visits.ow_type_id',
            'ow_plan_visits.shift_id',
            DB::raw('DATE_FORMAT(crm_ow_plan_visits.day, "%Y-%m-%d") as date'),
            DB::raw('TIME(crm_ow_plan_visits.day) as time'),
        )
            ->leftJoin('ow_actual_visits', 'ow_plan_visits.id', 'ow_actual_visits.ow_plan_id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('ow_plan_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', OwPlanVisit::class);
                }
            )
            ->where('ow_plan_visits.user_id', $user->id)
            ->whereDate('ow_plan_visits.day', Carbon::now())
            ->where('plan_visit_details.approval', 1)
            ->whereNull('ow_actual_visits.ow_plan_id')
            ->orderBy('id', 'ASC')
            ->groupBy('id', "plan_visit_details.approval")->get();
        return response()->json([
            "status" => 200,
            "data" => $planned_ow
        ], 200);
    }
    public function saveActuals(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();
        $userLines = $user->userLines()->pluck('id')->toArray();
        $data = collect([]);
        $visits = $request->visits;
        $message = '';
        // throw new CrmException()
        $company = Company::first()->name;
        $product_brand_level = ActualVisitSetting::where('key', 'actual_visit_level')->value('value');
        $copy_actual_visit_setting = ActualVisitSetting::where('key', 'copy_actual_visit')->value('value');
        $visits = collect($visits)->unique(function ($visit) {
            return $visit['visit_date'] . '-' . $visit['account_id'] . '-' . $visit['account_dr_id'] . '-' . $visit['visit_type_id'];
        })->values();
        Log::info($visits);
        foreach ($visits as $visit) {
            $savedVisit = DB::table('actual_visits')
                ->select('actual_visits.*')
                ->where('user_id', $user->id)
                ->whereDate('visit_date', $visit['visit_date'])
                ->where('account_dr_id', $visit['account_dr_id'])
                ->where('account_id', $visit['account_id'])
                ->first();
            if ($savedVisit) {
                $message = 'Actual Already Exists';
                $data = $data->push([
                    'visit_id' => $savedVisit->id ?? 0,
                    'offline_id' => $visit['offline_id'],
                    'sync_date' => $savedVisit->id ? Carbon::parse($savedVisit->created_at)->toDateString() : "",
                    'sync_time' => $savedVisit->id ? Carbon::parse($savedVisit->created_at)->format('H:i:s') : "",
                    "failure_reason" => "Actual Already Created Before"
                ]);
            }
            $insertion = date('Y-m-d H:i:s', strtotime($visit['insertion_date'] . $visit['insertion_time']));
            $date = date('Y-m-d H:i:s', strtotime($visit['visit_date'] . $visit['visit_time']));
            if (!$savedVisit) {
                $actualAndFailures = $this->addActualData($user, $visit, $date, $product_brand_level, $copy_actual_visit_setting, $insertion, $company, $userLines);
                $actual_visit = $actualAndFailures['actual_visit'];
                $failures = $actualAndFailures['failures'];
                $message = $actual_visit ? 'Actual Saved Successfully' : 'Actual visit has errors';
                $data = $data->push([
                    'visit_id' => $actual_visit?->id ?? -1,
                    'offline_id' => $visit['offline_id'],
                    'sync_date' => $actual_visit?->id ? Carbon::parse($actual_visit->created_at)->toDateString() : "",
                    'sync_time' => $actual_visit?->id ? Carbon::parse($actual_visit->created_at)->format('H:i:s') : "",
                    'failure_reason' => !$actual_visit ? implode(', ', $failures) : ""
                ]);
            }
        }
        return response()->json(['message' => $message, 'Data' => $data]);
    }
    public function addActualData($user, $visit, $date, $product_brand_level, $copy_actual_visit_setting, $insertion, $companyName, $userLines)
    {
        $failures = [];
        $checkDate = Carbon::parse($date)->toDateString() == '1970-01-01';
        if ($checkDate)  $failures[] = 'Invalid Date';
        $account = Account::find($visit['account_id']);
        if (!$account) $failures[] = 'Invalid Account ID';
        $doctor = Doctor::find($visit['account_dr_id']);
        if (!$doctor) $failures[] = 'Invalid Doctor ID';
        $line = null;
        $division = null;
        $brick = null;
        if ($companyName == 'Adamco') {
            $line_id = Product::find($visit['products'][0]['product_id'])->lineproducts()
                ->whereIntegerInRaw('line_products.line_id', $userLines)->first()?->line_id;
            $line = Line::find($line_id);
            $brick = $visit['brick_id'] != 'All'
                ? Brick::find($visit['brick_id'])
                : Brick::find(AccountLines::where('line_id', $line?->id)
                    ->where('account_id', $visit['account_id'])->first()?->brick_id);
            $division = LineDivision::find(LineBricks::where('line_id', $line?->id)->where('brick_id', $brick?->id)->first()->line_division_id);
        } else {
            $line = Line::find(LineDivision::find($visit['div_id'])?->line_id);
            $division = LineDivision::find($visit['div_id']);
            $brick = $visit['brick_id'] != 'All'
                ? Brick::find($visit['brick_id'])
                : Brick::find(AccountLines::where('line_id', LineDivision::find($visit['div_id'])->line_id)
                    ->where('line_division_id', $visit['div_id'])
                    ->where('account_id', $visit['account_id'])->first()?->brick_id);
        }
        if (!$line) $failures[] = 'Invalid Line ID';
        if (!$division) $failures[] = 'Invalid Division ID';
        // if (!$brick) $failures[] = 'Invalid Brick ID';
        $timezone = $line?->timezone;
        $futureDate = $date > Carbon::now($timezone)->addHours(1)->toDateTimeString();
        if ($futureDate) $failures[] = 'Invalid Visit Date';
        if ($visit['ll'] == '0.0' || $visit['lg'] == '0.0') $failures[] = 'Invalid Location';
        Log::channel('mobile_visits')->info($visit);
        $negativeTime = $this->durationTime($visit['visit_duration']);
        $actual_visit = null;
        if (
            $visit['ll'] != '0.0' &&
            $visit['lg'] != '0.0' &&
            !isNullable($account) &&
            !isNullable($doctor) &&
            !isNullable($line) &&
            !isNullable($division) &&
            !$futureDate &&
            !$checkDate
        ) {
            $actual_visit = ActualVisit::create([
                'user_id' => $user->id,
                'plan_id' => $visit['plan_id'] == 0 ? null : $visit['plan_id'],
                'line_id' => $line->id,
                'div_id' => $division->id,
                'brick_id' => $brick?->id,
                'acc_type_id' => $visit['account_type_id'],
                'account_id' => $account->id,
                'account_dr_id' => $doctor->id,
                'visit_type_id' => $visit['visit_type_id'],
                'visit_date' => $date,
                'end_visit_date' => $insertion,
                'll' => $visit['ll'],
                'lg' => $visit['lg'],
                'll_start' => $visit['ll_start'],
                'lg_start' => $visit['lg_start'],
                'visit_duration' => $negativeTime['formattedDiff'],
                'visit_status' => $negativeTime['falseTime'],
                'visit_deviation' => $visit['visit_deviation'],
                'is_web_visit' => 0,
                // 'shift_id' => $visit['shift_id'],
                'is_fake_gps' => $visit['is_fake_start_location'] || $visit['is_fake_end_location'] ? 1 : 0,
                'os_version' => $visit['os_version'],
                // 'device_brand' => $visit['device_name'],
                'os_type' => $visit['os_type'] == 1 ? 1 : 0,
            ]);
            foreach ($visit['products'] as $product) {
                VisitProduct::create([
                    'visit_id' => $actual_visit->id,
                    'product_id' => $product['product_id'],
                    'brand_id' => $product_brand_level == "Brand" ? Product::find($product['product_id'])?->brands->first()?->id : null,
                    'samples' => $product['samples'],
                    'notes' => $product['notes'] ?? null,
                    // 'payment' => $product['payment'] ?? null,
                    'follow_up' => $product['followup'] ?? null,
                    'market_feedback' => $product['market_feedback'] ?? null,
                    'vfeedback_id' => $product['vFeedback_id']
                ]);
                if (!empty($product['presentations'])) {
                    $totalDetailingTime = 0;
                    foreach ($product['presentations'] as $presentation) {
                        foreach ($presentation['slides'] as $slide) {
                            $start = Carbon::parse($actual_visit->visit_date)->setTimeFromTimeString($slide['start_time']);
                            $end = Carbon::parse($actual_visit->visit_date)->setTimeFromTimeString($slide['end_time']);
                            $detailing_time = $end->diffInSeconds($start);
                            $totalDetailingTime += $detailing_time;
                            Statistic::create([
                                'visit_id' => $actual_visit->id,
                                'product_id' => $product['product_id'],
                                'user_id' => $user->id,
                                'presentation_id' => $presentation['presentation_id'],
                                'slide_id' => $slide['slide_id'],
                                'start' => $start,
                                'end' => $end,
                                'rate' => $slide['rating'] ?? 0
                            ]);
                        }
                    }
                }
            }
            // Format total detailing time as H:i:s
            $formattedDetailingTime = gmdate('H:i:s', $totalDetailingTime);
            $actual_visit->update(['detailing_time' => $formattedDetailingTime]);
            if ($visit['visit_type_id'] == 2) {
                foreach ($visit['members'] as $manager) {
                    ActualVisitManager::create([
                        'visit_id' => $actual_visit->id,
                        'user_id' => $manager['emp_id'],
                    ]);
                    if ($copy_actual_visit_setting == 'Yes') {
                        $this->copyVisitForManager($manager['emp_id'], $visit, $date, $line->id, $division->id, $brick?->id, $product_brand_level, $insertion, $negativeTime);
                    }
                }
            }

            if ($visit['ll'] != '0.0' && $visit['lg'] != '0.0') {
                $location = AccountLines::where('line_id', $line->id)
                    ->where('account_id', $account->id)
                    ->where('line_division_id', $division->id)
                    ->first();
                if ($location->ll == null && $location->lg == null) {
                    $location->update([
                        'll' => $visit['ll'],
                        'lg' => $visit['lg'],
                        'visit_id' => $actual_visit->id,
                    ]);
                }
            }
            foreach ($visit['giveaways'] as $giveaway) {
                VisitGiveaway::create([
                    'visit_id' => $actual_visit->id,
                    'giveaway_id' => $giveaway['giveaway_id'],
                    'units' => $giveaway['units']
                ]);
            }
            if (!empty($visit['attachments'])) {
                foreach ($visit['attachments'] as $attachment) {
                    Attachment::create([
                        'attachable_id' => $actual_visit->id,
                        'attachable_type' => ActualVisit::class,
                        'path' => $attachment,
                    ]);
                }
            }
        }
        // if ($futureDate || $$negativeTime['falseTime']) {
        //     if (empty($visit['products'])) $failures[] = 'Empty Products';
        //     $savedVisit = DB::table('actual_visits')
        //         ->select('actual_visits.*')
        //         ->where('user_id', $user->id)
        //         ->where(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), Carbon::parse($visit['visit_date'])->toDateString())
        //         ->where('account_dr_id', $visit['account_dr_id'])
        //         ->where('account_id', $visit['account_id'])
        //         ->first();
        //     if ($savedVisit) {
        //         $actual_visit_error = ActualVisitError::create([
        //             'user_id' => $user->id,
        //             'plan_id' => $visit['plan_id'] == 0 ? null : $visit['plan_id'],
        //             'line_id' => $line->id,
        //             'div_id' => $division->id,
        //             'brick_id' => $brick?->id,
        //             'acc_type_id' => $visit['account_type_id'],
        //             'account_id' => $account->id,
        //             'account_dr_id' => $doctor->id,
        //             'visit_type_id' => $visit['visit_type_id'],
        //             'visit_date' => $date,
        //             'end_visit_time' => $insertion,
        //             'll' => $visit['ll'],
        //             'lg' => $visit['lg'],
        //             'll_start' => $visit['ll_start'],
        //             'lg_end' => $visit['lg_start'],
        //             'visit_duration' => $negativeTime['formattedDiff'],
        //             'invalid_duration' => $negativeTime['falseTime'],
        //             'visit_deviation' => $visit['visit_deviation'],
        //             'is_web_visit' => 0,
        //             'failures' => $failures,
        //             // 'is_fake_gps' => $visit['is_fake_start_location'] || $visit['is_fake_end_location'] ? 1 : 0,
        //             // 'os_version' => $visit['os_version'],
        //             // 'device_brand' => $visit['device_name'],
        //             // 'os_type' => $visit['os_type'] ? 1 : 0,
        //         ]);
        //         foreach ($visit['products'] as $product) {
        //             ActualVisitProductError::create([
        //                 'visit_id' => $actual_visit_error->id,
        //                 'product_id' => $product['product_id'],
        //                 'samples' => $product['samples'],
        //                 'notes' => $product['notes'] ?? null,
        //                 'follow_up' => $product['followup'] ?? null,
        //                 'market_feedback' => $product['market_feedback'] ?? null,
        //                 'vfeedback_id' => $product['vFeedback_id']
        //             ]);
        //         }
        //     }
        // }

        return array('actual_visit' => $actual_visit, 'failures' => $failures);
    }

    public function durationTime($visitDuration)
    {
        $duration = $visitDuration;
        list($hours, $minutes, $seconds) = explode(':', $duration);
        $hours = $hours;
        $minutes = $minutes;
        $seconds = $seconds;
        $falseTime = 0;
        if ($hours < 0 || $minutes < 0 || $seconds < 0) {
            $hours = abs($hours);
            $minutes =  abs($minutes);
            $seconds =  abs($seconds);
            $falseTime = 1;
        }
        // Format the difference without the minus sign
        $formattedDiff = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        return ['formattedDiff' => $formattedDiff, 'falseTime' => $falseTime];
    }


    public function copyVisitForManager($manager, $visit, $date, $line_id, $div_id, $brick_id, $product_brand_level, $insertion, $negativeTime)
    {
        $managerPlan = PlanVisit::where('account_id', $visit['account_id'])
            ->where('account_dr_id', $visit['account_dr_id'])
            ->whereDate('visit_date', Carbon::parse($date)->toDateString())
            ->where('user_id', $manager)
            ->whereHas('details', function ($q) {
                $q->where('approval', 1);
            })->first();
        $actual_visit = ActualVisit::create([
            'user_id' => $manager,
            'plan_id' => $managerPlan?->id ?? $visit['plan_id'],
            'line_id' => $line_id,
            'div_id' => $div_id,
            'brick_id' => $brick_id,
            'acc_type_id' => $visit['account_type_id'],
            'account_id' => $visit['account_id'],
            'account_dr_id' => $visit['account_dr_id'],
            'visit_type_id' => $visit['visit_type_id'],
            'visit_date' => $date,
            'end_visit_date' => $insertion,
            'll' => $visit['ll'],
            'lg' => $visit['lg'],
            'll_start' => $visit['ll_start'],
            'lg_start' => $visit['lg_start'],
            'visit_duration' => $negativeTime['formattedDiff'],
            'visit_status' => $negativeTime['falseTime'],
            'visit_deviation' => $visit['visit_deviation'],
            'is_web_visit' => 0,
            // 'shift_id' => $visit['shift_id'],
            'is_fake_gps' => $visit['is_fake_start_location'] || $visit['is_fake_end_location'] ? 1 : 0,
            'os_version' => $visit['os_version'],
            // 'device_brand' => $visit['device_name'],
            'os_type' => $visit['os_type'] == 1 ? 1 : 0,
        ]);

        foreach ($visit['products'] as $product) {
            VisitProduct::create([
                'visit_id' => $actual_visit?->id,
                'product_id' => $product['product_id'],
                'brand_id' => $product_brand_level == "Brand" ? Product::find($product['product_id'])?->brands?->first()?->id : null,
                'samples' => $product['samples'],
                'notes' => $product['notes'] ?? null,
                'follow_up' => $product['followup'] ?? null,
                // 'payment' => $product['payment'] ?? null,
                'market_feedback' => $product['market_feedback'] ?? null,
                'vfeedback_id' => $product['vFeedback_id']
            ]);
            if (!empty($product['presentations'])) {
                foreach ($product['presentations'] as $presentation) {
                    foreach ($presentation['slides'] as $slide) {
                        $start = date('Y-m-d H:i:s', strtotime(Carbon::parse($actual_visit->visit_date)->toDateString() . $slide['start_time']));
                        $end = date('Y-m-d H:i:s', strtotime(Carbon::parse($actual_visit->visit_date)->toDateString() . $slide['end_time']));
                        Statistic::create([
                            'visit_id' => $actual_visit->id,
                            'product_id' => $product['product_id'],
                            'user_id' => $manager,
                            'presentation_id' => $presentation['presentation_id'],
                            'slide_id' => $slide['slide_id'],
                            'start' => $start,
                            'end' => $end,
                            'rate' => $slide['rating'] ?? 0
                        ]);
                    }
                }
            }
        }
        foreach ($visit['giveaways'] as $giveaway) {
            VisitGiveaway::create([
                'visit_id' => $actual_visit->id,
                'giveaway_id' => $giveaway['giveaway_id'],
                'units' => $giveaway['units']
            ]);
        }
        if (!empty($visit['attachments'])) {
            foreach ($visit['attachments'] as $attachment) {
                Attachment::create([
                    'attachable_id' => $actual_visit->id,
                    'attachable_type' => ActualVisit::class,
                    'path' => $attachment,
                ]);
            }
        }
    }
    public function saveAOw(Request $request)
    {
        $user = Auth::user();
        $data = collect([]);
        $ows = $request->ows;
        foreach ($ows as $ow) {
            $savedOw = DB::table('ow_actual_visits')
                ->select('ow_actual_visits.*')
                ->where('user_id', $user->id)
                ->where(DB::raw("(DATE_FORMAT(date,'%Y-%m-%d'))"), Carbon::parse($ow['date'])->toDateString())
                ->where('shift_id', $ow['shift_id'])
                ->where('ow_type_id', $ow['ow_type_id'])
                ->first();
            if ($savedOw) {
                $data = $data->push([
                    'ow_id' => $savedOw->id ?? 0,
                    'offline_id' => $ow['offline_id'],
                    'sync_date' => $savedOw->id ? Carbon::parse($savedOw->created_at)->toDateString() : "",
                    'sync_time' => $savedOw->id ? Carbon::parse($savedOw->created_at)->format('H:i:s') : ""
                ]);
            }
            if (!$savedOw) {
                $date = date('Y-m-d H:i', strtotime($ow['date'] . $ow['time']));
                $office_work = OwActualVisit::create([
                    'user_id' => $user->id,
                    'date' => $date,
                    'ow_type_id' => $ow['ow_type_id'],
                    'shift_id' => $ow['shift_id'],
                    'notes' => $ow['notes'],
                    'ow_plan_id' => $ow['ow_plan_id'] == 0 ? null : $ow['ow_plan_id'],
                ]);
                $data = $data->push([
                    'ow_id' => $office_work->id ?? 0,
                    'offline_id' => $ow['offline_id'],
                    'sync_date' => $office_work->id ? Carbon::parse($office_work->created_at)->toDateString() : "",
                    'sync_time' => $office_work->id ? Carbon::parse($office_work->created_at)->format('H:i:s') : ""
                ]);
            }
        }
        return response()->json(['message' => 'OW Saved Successfully', 'Data' => $data]);
    }
    public function savePlans(Request $request)
    {
        $user = Auth::user();
        $data = collect([]);
        $plans = $request->plans;
        
        $line = Line::find($plans[0]['line_id']);
        $hasApprovable = $this->checkApprovable($user, $line);
        foreach ($plans as $plan) {
            $savedPlan = DB::table('planned_visits')
                ->select('planned_visits.*')
                ->where('user_id', $user->id)
                ->where(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), Carbon::parse($plan['visit_date'])->toDateString())
                ->where('account_dr_id', $plan['account_dr_id'])
                ->where('account_id', $plan['account_id'])
                ->first();
            if ($savedPlan) {
                $data = $data->push([
                    'visit_id' => $savedPlan->id ?? 0,
                    'offline_id' => $plan['offline_id'],
                    'sync_date' => $savedPlan->id ? Carbon::parse($savedPlan->created_at)->toDateString() : "",
                    'sync_time' => $savedPlan->id ? Carbon::parse($savedPlan->created_at)->format('H:i:s') : ""
                ]);
            }
            $date = date('Y-m-d H:i:s', strtotime($plan['visit_date'] . '00:00:00'));
            if (!$savedPlan) {
                $plan_visit = $this->addPlanData($user, $plan, $date, $line, $hasApprovable);
                // Log::info($plan_visit);
                $data = $data->push([
                    'visit_id' => $plan_visit['id'] ?? -1,
                    'offline_id' => $plan['offline_id'],
                    'sync_date' => $plan_visit['id'] ? Carbon::parse($plan_visit->created_at)->toDateString() : "",
                    'sync_time' => $plan_visit['id'] ? Carbon::parse($plan_visit->created_at)->format('H:i:s') : "",
                    'failure_reason' => $plan_visit['id'] < 0 ? 'There is something error at this plan.' : null
                ]);
            }
        }
        return response()->json(['message' => 'Plans Saved Successfully', 'Data' => $data]);
    }
    public function addPlanData($user, $plan, $date, $line, $hasApprovable)
    {
        $account = Account::find($plan['account_id']);
        $accountType = AccountType::find($plan['account_type_id']);
        $doctor = Doctor::find($plan['account_dr_id']);
        $division = LineDivision::find($plan['div_id']);
        // Log::info($plan);
        if (!isNullable($account) && !isNullable($doctor) && !isNullable($line) && !isNullable($division) && !isNullable($accountType)) {
            $planned = PlanVisit::create([
                'user_id' => $user->id,
                'line_id' => $plan['line_id'],
                'div_id' => $plan['div_id'],
                'shift_id' => $accountType?->shift_id,
                'account_id' => $plan['account_id'],
                'account_dr_id' => $plan['account_dr_id'],
                'visit_type' => 1,
                'visit_date' => $date,
            ]);
            PlanVisitDetails::firstOrCreate(
                [
                    'visitable_id' => $planned->id,
                    'visitable_type' => PlanVisit::class,
                ],
                [
                    'approval' => $hasApprovable ? null : 1
                ]
            );
            return $planned;
        } else {
            return null;
        }
    }
    private function checkApprovable(User $user, Line $line)
    {
        if ($user->hasPosition() && !$user->hasDivision($line)) {
            return $user->position()->planable->first()->approvables()->wherePivot('request_type', PlanVisit::class)->exists();
        }
        return $user->division($line)?->divisionType?->planable?->where('line_id', $line->id)->first()
            ->approvables()->wherePivot('request_type', PlanVisit::class)->exists();
    }
    public function store_ow(Request $request)
    {
        $user = Auth::user();
        $data = collect([]);
        $ows = $request->ows;
        foreach ($ows as $ow) {
            $savedOw = DB::table('ow_actual_visits')
                ->select('ow_actual_visits.*')
                ->where('user_id', $user->id)
                ->where(DB::raw("(DATE_FORMAT(date,'%Y-%m-%d'))"), Carbon::parse($ow['date'])->toDateString())
                ->where('shift_id', $ow['shift_id'])
                ->where('ow_type_id', $ow['ow_type_id'])
                ->first();
            if ($savedOw) {
                $data = $data->push([
                    'ow_id' => $savedOw->id ?? 0,
                    'offline_id' => $ow['offline_id'],
                    'sync_date' => $savedOw->id ? Carbon::parse($savedOw->created_at)->toDateString() : "",
                    'sync_time' => $savedOw->id ? Carbon::parse($savedOw->created_at)->format('H:i:s') : ""
                ]);
            }
            if (!$savedOw) {
                $date = date('Y-m-d H:i', strtotime($ow['date'] . $ow['time']));
                $office_work = OwActualVisit::create([
                    'user_id' => $user->id,
                    'date' => $date,
                    'ow_type_id' => $ow['ow_type_id'],
                    'shift_id' => $ow['shift_id'],
                    'notes' => $ow['notes'],
                    'ow_plan_id' => $ow['ow_plan_id'] == 0 ? null : $ow['ow_plan_id'],
                ]);
                $data = $data->push([
                    'ow_id' => $office_work->id ?? 0,
                    'offline_id' => $ow['offline_id'],
                    'sync_date' => $office_work->id ? Carbon::parse($office_work->created_at)->toDateString() : "",
                    'sync_time' => $office_work->id ? Carbon::parse($office_work->created_at)->format('H:i:s') : ""
                ]);
            }
        }
        return response()->json(['message' => 'OW Saved Successfully', 'Data' => $data]);
    }

    public function presentations()
    {
        /**@var User */
        $user = Auth::user();
        $lines = $user->userLines();
        $presentations = collect([]);
        $slides = collect([]);
        foreach ($lines as $line) {
            $line->products()->get()->each(function ($product) use (&$presentations) {
                $presentations = $presentations->merge($product->presentations);
            });
        }
        $presentations = $presentations->map(function ($presentation) {
            return [
                'name' => $presentation->name,
                'product_id' => $presentation?->pivot?->product_id,
                'presentation_id' => $presentation->id,
            ];
        });
        collect($presentations)->each(function ($presentation) use (&$slides) {
            $presentation = Presentation::find($presentation['presentation_id']);
            $slides = $slides->merge($presentation->slides);
        });
        // throw new CrmException($slides);
        $slides = $slides->map(function ($slide) {
            return [
                'slide_id' => $slide->id,
                'slide_path' => $slide->attachment?->url,
                'slide_type' => $slide->media_type,
                'thumbnail_path' => $slide->thumbnail?->attachment?->path,
                'thumbnail_id' => $slide->thumbnail_id,
                'presentation_id' => $slide->presentation_id,
            ];
        });
        return response()->json([
            "status" => 200,
            "data" => [
                "presentations" => $presentations,
                "slides" => $slides
            ]
        ], 200);
        // throw new CrmException();
    }
    public function slides()
    {
        /**@var User */
        $user = Auth::user();
        $lines = $user->userLines();
        $presentations = collect([]);
        $slides = collect([]);
        foreach ($lines as $line) {
            $line->products()->get()->each(function ($product) use (&$presentations) {
                $presentations = $presentations->merge($product->presentations);
            });
        }
        $presentations = $presentations->map(function ($presentation) {
            return [
                'id' => $presentation->id,
                'name' => $presentation->name,
                'product_id' => $presentation?->pivot?->product_id,
            ];
        });
        collect($presentations)->each(function ($presentation) use (&$slides) {
            $presentation = Presentation::find($presentation['id']);
            $slides = $slides->merge($presentation->slides);
        });
        // throw new CrmException($slides);
        $slides = $slides->map(function ($slide) {
            return [
                'id' => $slide->id,
                'slide_path' => $slide->attachment?->path,
                'slide_type' => $slide->media_type,
                'thumbnail_path' => $slide->thumbnail?->attachment?->path,
                'thumbnail_id' => $slide->thumbnail_id,
                'presentation_id' => $slide->presentation_id,
            ];
        });
        return response()->json([
            "status" => 200,
            "data" => [
                "presentations" => $presentations,
                "slides" => $slides
            ]
        ], 200);
    }


    // pending plans
    public function getPendingPlans()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();

        $users = collect([]);
        foreach ($lines as $line) {
            $users = $users->merge($user->planableUsers($line->id, PlanVisit::class, DivisionType::class));
            $users = $users->merge($user->planableUsers($line->id, PlanVisit::class, Position::class));
        }
        $userIds = $users->unique('id')->values()->pluck('id')->toArray();
        $plans = DB::table('planned_visits')->select(
            'planned_visits.id',
            'lines.id as line_id',
            'line_divisions.id as division_id',
            DB::raw('IFNULL(crm_bricks.id,-1) as brick_id'),
            'planned_visits.account_id as account_id',
            'accounts.name as account',
            'users.id as user_id',
            'users.fullname as employee',
            'accounts.type_id as type_id',
            'account_types.name as account_type',
            'planned_visits.account_dr_id as doctor_id',
            'doctors.name as doctor',
            'doctors.speciality_id as speciality_id',
            DB::raw('IFNULL(crm_d.id,"") as doc_class'),
            DB::raw('IFNULL(group_concat(distinct crm_account_lines.ll),"") as ll'),
            DB::raw('IFNULL(group_concat(distinct crm_account_lines.lg),"") as lg'),
            DB::raw('CAST(IFNULL(crm_planned_visits.shift_id,3) AS UNSIGNED) as shift_id'),
            DB::raw('IFNULL(group_concat(distinct crm_a.id),"") as acc_class'),
            'visit_types.name as type',
            'visit_types.id as visit_type_id',
            DB::raw('DATE_FORMAT(crm_planned_visits.visit_date, "%Y-%m-%d") as date'),
            DB::raw('TIME(crm_planned_visits.visit_date) as time'),

        )

            ->leftJoin('lines', 'planned_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'planned_visits.div_id', 'line_divisions.id')
            ->leftJoin('accounts', 'planned_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', function ($join) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->on('lines.id', 'account_lines.line_id')
                    ->on('line_divisions.id', 'account_lines.line_division_id')
                    ->where('account_lines.from_date', '<=', now())
                    ->where(fn($q) => $q->where('account_lines.to_date', '>', (string) Carbon::now())
                        ->orWhere('account_lines.to_date', null));
            })
            ->leftJoin('doctors', 'planned_visits.account_dr_id', 'doctors.id')
            ->leftJoin('users', 'planned_visits.user_id', 'users.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('visit_types', 'planned_visits.visit_type', 'visit_types.id')
            ->leftJoin(
                'actual_visits',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'actual_visits.plan_id');
                    $join->on('planned_visits.user_id', '=', 'actual_visits.user_id');
                }
            )
            ->join(
                'plan_visit_details',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', PlanVisit::class);
                }
            )
            ->whereIntegerInRaw('planned_visits.user_id', $userIds)
            ->whereDate('planned_visits.visit_date', '>=', Carbon::now())
            ->whereNull('plan_visit_details.approval')
            ->whereNull('actual_visits.plan_id')
            ->orderBy('id', 'ASC')
            ->groupBy('id', "plan_visit_details.approval", "bricks.id")
            ->get()->unique('id')->values()->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'line_id' => $plan->line_id,
                    'division_id' => $plan->division_id,
                    'user_id' => $plan->user_id,
                    'employee' => $plan->employee,
                    'brick_id' => $plan->brick_id,
                    'account_id' => $plan->account_id,
                    'account' => $plan->account,
                    'type_id' => $plan->type_id,
                    'account_type' => $plan->account_type,
                    'doctor_id' => $plan->doctor_id,
                    'doctor' => $plan->doctor,
                    'doc_class' => $plan->doc_class,
                    'speciality_id' => $plan->speciality_id,
                    'll' => $plan->ll,
                    'lg' => $plan->lg,
                    'shift_id' => $plan->shift_id,
                    'acc_class' => explode(',', $plan->acc_class)[0],
                    'type' => $plan->type,
                    'visit_type_id' => $plan->visit_type_id,
                    'date' => $plan->date,
                    'time' => $plan->time,
                ];
            });
        return response()->json([
            "status" => 200,
            "data" => $plans
        ], 200);
    }

    public function saveApprovedPlans(Request $request)
    {
        $user = Auth::user();
        $plans = $request->plans;
        $data = collect([]);
        foreach ($plans as $plan) {
            $detail = PlanVisitDetails::where('visitable_id', $plan)->where('visitable_type', PlanVisit::class)->first();

            $detail->update([
                'approval' => 1,
                'user_id' => $user->id
            ]);
            $data = $data->push([
                'visit_id' => $detail?->id ?? 0,
                'offline_id' => $plan,
                'sync_date' => Carbon::parse($detail?->updated_at)->toDateString() ?? "",
                'sync_time' => Carbon::parse($detail?->updated_at)->format('H:i:s') ?? ""
            ]);
        }
        return response()->json([
            "status" => 200,
            "data" => $data
        ], 200);
    }
    public function usageLogs(Request $request)
    {
        $data = [];
        $locations = $request->locations;
        // Log::info($locations);
        $user = Auth::user();
        $id = 1;
        foreach ($locations as $location) {
            $date = date('Y-m-d H:i', strtotime($location['date'] . $location['time']));

            // $online = UsageLog::create([
            //     'user_id' => $user->id,
            //     'll' => $location['ll'],
            //     'lg' => $location['lg'],
            //     // 'os_type' => $location['os_type'],
            //     'device_type' => $location['device_name'],
            //     'date' => $date,
            // ]);

            $this->tracingService->processLocationTracking($location);

            $data[] = [
                'online_id' => $id,
                'offline_id' => $location['offline_id'],
                'sync_date' => Carbon::now()->toDateString(),
                'sync_time' => Carbon::now()->format('H:i:s')
            ];
            $id++;
        }


        return response()->json(['message' => 'Log Saved Successfully', 'data' => $data]);
    }
}
